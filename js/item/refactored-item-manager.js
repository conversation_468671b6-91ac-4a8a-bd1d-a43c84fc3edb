/**
 * 重构后的道具管理器
 * 使用组合模式，将功能分解到专门的子系统中
 */
import Emitter from '../libs/tinyemitter.js';
import { BLOCK_COLORS, BLOCK_EFFECTS } from '../game/block.js';

// 子系统
import CooldownSystem from './systems/cooldown-system.js';
import UpgradeSystem from './systems/upgrade-system.js';
import TargetingSystem from './systems/targeting-system.js';

// 道具实现
import FireballItem from './items/fireball-item.js';
// 提取的道具实现
import LightningItem from './items/lightning-item.js';
import WaterflowItem from './items/waterflow-item.js';
import EarthquakeItem from './items/earthquake-item.js';

// 提取的渲染器
import LightningRenderer from './animations/lightning-renderer.js';
import EffectRenderer from './animations/effect-renderer.js';

// 动画渲染器
import ExplosionRenderer from './animations/explosion-renderer.js';

// 道具类型
export const ITEM_TYPES = {
  FIREBALL: 'fireball',
  LIGHTNING: 'lightning',
  WATERFLOW: 'waterflow',
  EARTHQUAKE: 'earthquake'
};

export default class RefactoredItemManager extends Emitter {
  /**
   * 创建重构后的道具管理器
   * @param {Grid} grid - 游戏网格
   * @param {Object} options - 配置选项
   */
  constructor(grid, options = {}) {
    super();
    
    this.grid = grid;
    
    // 依赖注入，减少全局耦合
    this.dependencies = {
      getGameController: options.getGameController || (() => GameGlobal.gameController),
      getMusicManager: options.getMusicManager || (() => GameGlobal.musicManager),
      getTetrominoClass: options.getTetrominoClass || (() => GameGlobal.Tetromino),
      getGrid: options.getGrid || (() => this.grid) // 添加网格获取方法
    };
    
    // 初始化子系统
    this.cooldownSystem = new CooldownSystem();
    this.upgradeSystem = new UpgradeSystem();
    this.targetingSystem = new TargetingSystem(grid);
    
        // 初始化道具实现（包含提取的完整实现）
    this.items = {
      [ITEM_TYPES.FIREBALL]: new FireballItem(grid, this.targetingSystem),
      [ITEM_TYPES.LIGHTNING]: new LightningItem(grid, this.targetingSystem),
      [ITEM_TYPES.WATERFLOW]: new WaterflowItem(grid, this.targetingSystem),
      [ITEM_TYPES.EARTHQUAKE]: new EarthquakeItem(grid, this.targetingSystem)
    };

    // 初始化渲染器（包含提取的完整渲染器）
    this.explosionRenderer = new ExplosionRenderer();
    this.lightningRenderer = new LightningRenderer(grid);
    this.effectRenderer = new EffectRenderer();

    // 动画状态（支持所有道具效果）
    this.animations = {
      timer: 0,
      isActive: false,
      type: null, // 动画类型，用于区分处理逻辑
      explosionEffect: null,
      lightningEffect: null,
      waterflowEffect: null,
      earthquakeEffect: null
    };

    // 道具激活的方块
    this.affectedBlocks = new Set();

    // 保存的游戏状态
    this.savedGameState = null;

    // 防止重复处理的标志
    this._isProcessingEffect = false;
  }

  /**
   * 使用道具
   * @param {string} itemType - 道具类型
   * @param {number} row - 目标行
   * @param {number} col - 目标列
   * @returns {boolean} 是否成功使用
   */
  useItem(itemType, row = 0, col = 0) {
    console.log(`🛠️ 使用道具 ${itemType} 位置: (${row}, ${col})`);

    // 检查道具是否可以使用
    if (!this._canUseItem(itemType)) {
      console.log(`道具 ${itemType} 无法使用`);
      return false;
    }
    
    // 获取道具等级
    const level = this.upgradeSystem.getItemLevel(itemType);
    
    // 保存游戏状态
    this._saveGameState();
    
        // 使用道具（支持所有道具类型）
    let success = false;
    switch (itemType) {
      case ITEM_TYPES.FIREBALL:
        success = this._useFireball(row, col, level);
        break;
      case ITEM_TYPES.LIGHTNING:
        success = this._useLightning(row, col, level);
        break;
      case ITEM_TYPES.WATERFLOW:
        success = this._useWaterflow(row, col, level);
        break;
      case ITEM_TYPES.EARTHQUAKE:
        success = this._useEarthquake(row, col, level);
        break;
      default:
        console.warn(`未知道具类型: ${itemType}`);
        break;
    }
    
    if (success) {
      // 应用冷却和消耗使用次数
      this._applyItemCooldownAndUse(itemType);
      
      // 计算分数
      const blocksCount = this.affectedBlocks.size;
      const score = this._calculateItemScore(itemType, blocksCount);
      
      // 触发事件
      this.emit('item-used', {
        type: itemType,
        level: level,
        blocksAffected: blocksCount,
        score: score
      });
    } else {
      // 恢复游戏状态
      this._restoreGameState();
    }
    
    return success;
  }

  /**
   * 使用火球术
   */
  _useFireball(row, col, level) {
    const fireballItem = this.items[ITEM_TYPES.FIREBALL];
    
    const callbacks = {
      markBlockAffected: (block) => this._markBlockAffected(block),
      createExplosionEffect: (centerRow, centerCol, range) => {
        const effect = this.explosionRenderer.createExplosionEffect(
          centerRow, centerCol, range, this.grid, level
        );
        this.animations.explosionEffect = effect;
        this._startAnimation('fireball');
      },
      playSound: (soundType) => this._playSound(soundType)
    };
    
    return fireballItem.use(row, col, level, callbacks);
  }
  /**
   * 使用闪电链（从原始实现提取）
   */
  _useLightning(row, col, level) {
    const lightningItem = this.items[ITEM_TYPES.LIGHTNING];
    
    const callbacks = {
      markBlockAffected: (block) => this._markBlockAffected(block),
      createLightningEffect: (startRow, startCol, chainPath) => {
        // 获取目标方块的颜色
        const targetBlock = this.grid.getBlock(startRow, startCol);
        const color = targetBlock ? targetBlock.color : 'blue';

        const effect = this.lightningRenderer.createLightningEffect(
          startRow, startCol, chainPath, color, level
        );
        this.animations.lightningEffect = effect;
        this._startAnimation('lightning');
      },
      playSound: (soundType) => this._playSound(soundType)
    };
    
    return lightningItem.use(row, col, level, callbacks);
  }

  /**
   * 使用激流（从原始实现提取）
   */
  _useWaterflow(row, col, level) {
    const waterflowItem = this.items[ITEM_TYPES.WATERFLOW];
    
    const callbacks = {
      markBlockAffected: (block) => this._markBlockAffected(block),
      createWaterflowEffect: (affectedRows, effectLevel) => {
        const effect = this.effectRenderer.createWaterflowEffect(
          affectedRows, effectLevel
        );
        this.animations.waterflowEffect = effect;
        this._startAnimation('waterflow');
      },
      playSound: (soundType) => this._playSound(soundType)
    };
    
    return waterflowItem.use(row, col, level, callbacks);
  }

  /**
   * 使用地震术（从原始实现提取）
   */
  _useEarthquake(row, col, level) {
    const earthquakeItem = this.items[ITEM_TYPES.EARTHQUAKE];
    
    const callbacks = {
      markBlockAffected: (block) => this._markBlockAffected(block),
      createEarthquakeEffect: (startRow, endRow, intensity) => {
        const effect = this.effectRenderer.createEarthquakeEffect(
          startRow, endRow, intensity
        );
        this.animations.earthquakeEffect = effect;
        this._startAnimation('earthquake');
      },
      playSound: (soundType) => this._playSound(soundType)
    };
    
    return earthquakeItem.use(row, col, level, callbacks);
  }

  /**
   * 检查道具是否可以使用
   */
  _canUseItem(itemType) {
    // 检查调试配置
    const debugConfig = this._getDebugConfig();

    // 检查是否已解锁
    if (!this.upgradeSystem.isItemUnlocked(itemType)) {
      console.log(`道具 ${itemType} 尚未解锁`);
      return false;
    }

    // 检查使用次数（调试模式下跳过）
    if (!debugConfig.getConfig('debug.infiniteItems') && !this.upgradeSystem.canUseItem(itemType)) {
      console.log(`道具 ${itemType} 使用次数已用完`);
      return false;
    }

    // 检查冷却时间（调试模式下跳过）
    if (!debugConfig.getConfig('debug.noCooldown') && !this.cooldownSystem.isItemReady(itemType)) {
      console.log(`道具 ${itemType} 冷却中`);
      return false;
    }

    return true;
  }

  /**
   * 应用道具冷却和使用次数
   */
  _applyItemCooldownAndUse(itemType) {
    const debugConfig = this._getDebugConfig();
    
    // 应用冷却时间（调试模式下跳过）
    if (!debugConfig.getConfig('debug.noCooldown')) {
      this.cooldownSystem.applyCooldown(itemType);
    }
    
    // 减少使用次数（调试模式下跳过）
    if (!debugConfig.getConfig('debug.infiniteItems')) {
      this.upgradeSystem.useItem(itemType);
    }
  }

  /**
   * 设置当前关卡
   */
  setCurrentLevel(levelId) {
    this.upgradeSystem.setCurrentLevel(levelId);
  }

  /**
   * 升级道具
   */
  upgradeItem(itemType) {
    return this.upgradeSystem.upgradeItem(itemType);
  }

  /**
   * 获取道具信息
   */
  getItemInfo(itemType) {
    const upgradeInfo = this.upgradeSystem.getItemInfo(itemType);
    const cooldownProgress = this.cooldownSystem.getCooldownProgress(itemType);
    const isReady = this.cooldownSystem.isItemReady(itemType);

    return {
      ...upgradeInfo,
      cooldownProgress,
      isReady,
      hasUses: upgradeInfo.canUse // gameinfo.js期望的属性名
    };
  }

    /**
   * 渲染所有效果（支持所有道具效果）
   */
  render(ctx) {
    if (!ctx) return;

    let hasActiveAnimations = false;

    // 渲染爆炸效果
    if (this.animations.explosionEffect) {
      hasActiveAnimations = true;
      const stillActive = this.explosionRenderer.render(ctx, this.animations.explosionEffect);
      if (!stillActive) {
        console.log('🎬 爆炸动画完成');
        this.animations.explosionEffect = null;
      }
    }

    // 渲染闪电效果
    if (this.animations.lightningEffect) {
      hasActiveAnimations = true;
      const stillActive = this.lightningRenderer.render(ctx, this.animations.lightningEffect);
      if (!stillActive) {
        console.log('⚡ 闪电动画完成');
        this.animations.lightningEffect = null;
      }
    }

    // 渲染水流和地震效果
    this.effectRenderer.renderAll(ctx);

    // 清理已完成的效果
    this.lightningRenderer.cleanup();
    this.effectRenderer.cleanup();

    // 调试信息（仅在有活跃动画时输出）
    if (hasActiveAnimations && Math.random() < 0.1) { // 10%概率输出，避免刷屏
      console.log('🎨 正在渲染道具动画效果');
    }
  }

  /**
   * 更新动画状态和处理道具效果完成
   */
  update() {
    // 更新冷却时间
    this.cooldownSystem.update();

    // 更新动画
    if (this.animations.isActive) {
      this.animations.timer++;

      // 动画结束（10帧）
      if (this.animations.timer >= 10) {
        // 先清理动画状态，防止重复触发
        this.animations.isActive = false;
        this.animations.timer = 0;
        const animationType = this.animations.type;
        this.animations.type = null;

        // 清理所有动画效果
        this.animations.explosionEffect = null;
        this.animations.lightningEffect = null;
        this.animations.waterflowEffect = null;
        this.animations.earthquakeEffect = null;

        // 清理渲染器中的活跃效果
        this.lightningRenderer.activeEffects = [];
        this.effectRenderer.activeEffects = [];

        // 根据动画类型进行不同处理
        if (animationType === 'earthquake') {
          console.log('🌍 地震术动画结束，仅清理状态');
          this.affectedBlocks.clear();
          this._triggerMatchCheck();
          this._restoreGameState();
        } else if (this.affectedBlocks.size > 0) {
          // 其他道具需要移除受影响的方块（只有在有受影响方块时才处理）
          console.log(`🎬 ${animationType}动画结束，处理${this.affectedBlocks.size}个受影响的方块`);
          this._finalizeItemEffect();
        } else {
          // 没有受影响的方块，直接恢复状态
          console.log(`🎬 ${animationType}动画结束，没有受影响的方块`);
          this._restoreGameState();
        }
      }
    }

    this._updateAnimations();
  }

  /**
   * 更新动画状态
   */
  _updateAnimations() {
    // 检查各种动画效果
    const hasExplosion = !!this.animations.explosionEffect;
    const hasLightning = !!this.animations.lightningEffect;
    const hasWaterflow = !!this.animations.waterflowEffect;
    const hasEarthquake = !!this.animations.earthquakeEffect;
    const hasLightningRenderer = this.lightningRenderer.activeEffects.length > 0;
    const hasEffectRenderer = this.effectRenderer.activeEffects.length > 0;

    const wasActive = this.animations.isActive;
    const shouldBeActive = hasExplosion || hasLightning || hasWaterflow || hasEarthquake || hasLightningRenderer || hasEffectRenderer;

    // 如果动画类型为null但仍然被认为活跃，强制设置为false
    if (!this.animations.type && shouldBeActive) {
      console.log('⚠️ 检测到动画类型为null但仍有活跃效果，强制清理');
      this.animations.explosionEffect = null;
      this.animations.lightningEffect = null;
      this.animations.waterflowEffect = null;
      this.animations.earthquakeEffect = null;
      this.lightningRenderer.activeEffects = [];
      this.effectRenderer.activeEffects = [];
      this.animations.isActive = false;
      return;
    }

    this.animations.isActive = shouldBeActive;

    // 调试信息（仅在状态变化时输出）
    if (wasActive !== shouldBeActive) {
      console.log(`🎬 动画状态变化: ${wasActive} → ${shouldBeActive}, 类型: ${this.animations.type}`);
      if (shouldBeActive) {
        console.log(`  - 爆炸:${hasExplosion}, 闪电:${hasLightning}, 激流:${hasWaterflow}, 地震:${hasEarthquake}`);
        console.log(`  - 闪电渲染器:${hasLightningRenderer}, 效果渲染器:${hasEffectRenderer}`);
      }
    }
  }

  /**
   * 计算道具分数
   */
  _calculateItemScore(itemType, blocksCount) {
    const baseScore = blocksCount * 100;
    const levelMultiplier = this.upgradeSystem.getItemLevel(itemType);
    return baseScore * levelMultiplier;
  }

  /**
   * 标记方块受影响
   */
  _markBlockAffected(block) {
    if (!block) return;

    // 如果是护盾方块，先消耗护盾效果
    if (block.effect === 'shield') {
      const result = block.applyEffect();
      if (result && result.blocked) {
        console.log(`护盾方块阻挡了道具攻击: (${block.row}, ${block.col})`);
        return; // 护盾阻挡攻击，方块不被消除
      }
    }
    // 如果是冰冻方块，先消耗冰冻效果
    else if (block.effect === 'frozen') {
      block.applyEffect();
    }

    // 标记为受影响，准备消除
    this.affectedBlocks.add(block);
    // 启动消除动画
    if (block.startDestroyAnimation) {
      block.startDestroyAnimation();
    }
  }

  /**
   * 完成道具效果，移除受影响的方块
   */
  _finalizeItemEffect() {
    // 防止重复调用
    if (this._isProcessingEffect) {
      console.log('⚠️ 道具效果正在处理中，跳过重复调用');
      return;
    }

    // 如果没有受影响的方块，直接返回
    if (this.affectedBlocks.size === 0) {
      console.log('没有受影响的方块，跳过处理');
      this._restoreGameState();
      return;
    }

    // 设置处理标志
    this._isProcessingEffect = true;

    // 记录需要检查的列和移除的方块位置
    const affectedCols = new Set();
    const removedPositions = [];

    console.log(`处理${this.affectedBlocks.size}个受影响的方块`);

    // 收集被移除的方块位置
    for (const block of this.affectedBlocks) {
      if (block && typeof block.row === 'number' && typeof block.col === 'number') {
        // 标记列为受影响
        affectedCols.add(block.col);

        // 记录位置
        removedPositions.push({
          row: block.row,
          col: block.col
        });
      }
    }

    // 触发方块移除事件
    this.emit('blocks:removed');

    // 使用较短的等待时间，确保动画有足够时间显示但不会卡住游戏
    setTimeout(() => {
      this._processBlockRemoval(affectedCols, removedPositions);
    }, 150);
  }

  /**
   * 启动动画
   */
  _startAnimation(type) {
    this.animations.isActive = true;
    this.animations.timer = 0;
    this.animations.type = type;
    console.log(`🎬 启动${type}动画`);
  }

  /**
   * 播放音效
   */
  _playSound(soundType) {
    try {
      const musicManager = this.dependencies.getMusicManager();
      if (musicManager && musicManager.playItemSound) {
        musicManager.playItemSound(soundType);
      }
    } catch (e) {
      console.warn('播放音效失败:', e);
    }
  }

  /**
   * 获取调试配置
   */
  _getDebugConfig() {
    if (!this._debugConfig) {
      const hasGlobalDebug = (typeof global !== 'undefined' && global.isDebugMode) || 
                            (typeof window !== 'undefined' && window.isDebugMode);
      
      if (hasGlobalDebug) {
        const debugModule = (typeof global !== 'undefined') ? global : window;
        this._debugConfig = {
          isDebugMode: debugModule.isDebugMode || (() => false),
          getConfig: (path) => {
            if (path === 'debug.infiniteItems') {
              return debugModule.isDebugMode && debugModule.isDebugMode();
            }
            if (path === 'debug.noCooldown') {
              return debugModule.isDebugMode && debugModule.isDebugMode();
            }
            return false;
          }
        };
      } else {
        this._debugConfig = {
          isDebugMode: () => false,
          getConfig: () => false
        };
      }
    }
    return this._debugConfig;
  }

  /**
   * 保存游戏状态
   */
  _saveGameState() {
    try {
      const gameController = this.dependencies.getGameController();
      if (gameController && gameController.gameState) {
        // 确保gameState存在且可序列化
        const stateToSave = gameController.gameState;
        if (stateToSave && typeof stateToSave === 'object') {
          this.savedGameState = JSON.parse(JSON.stringify(stateToSave));
        } else {
          console.warn('游戏状态不可序列化，跳过保存');
          this.savedGameState = null;
        }
      } else {
        console.warn('游戏控制器或游戏状态不存在，跳过保存');
        this.savedGameState = null;
      }
    } catch (e) {
      console.warn('保存游戏状态失败:', e);
      this.savedGameState = null;
    }
  }

  /**
   * 恢复游戏状态
   */
  _restoreGameState() {
    try {
      if (this.savedGameState) {
        const gameController = this.dependencies.getGameController();
        if (gameController && gameController.gameState) {
          // 只有在有有效保存状态时才恢复
          gameController.gameState = this.savedGameState;
          console.log('游戏状态已恢复');
        } else {
          console.warn('游戏控制器不存在，无法恢复状态');
        }
      } else {
        console.log('没有保存的游戏状态需要恢复');
      }
    } catch (e) {
      console.warn('恢复游戏状态失败:', e);
    } finally {
      this._clearSavedState();
    }
  }

  /**
   * 清理保存的状态
   */
  _clearSavedState() {
    this.savedGameState = null;
    this.savedTetromino = null;
  }

  /**
   * 清理处理标志
   */
  _clearProcessingFlag() {
    this._isProcessingEffect = false;
  }

  // ===== 兼容性方法（保持向后兼容） =====
  
  isItemUnlocked(itemType) {
    return this.upgradeSystem.isItemUnlocked(itemType);
  }

  getUnlockedItems() {
    return this.upgradeSystem.getUnlockedItems();
  }

  isItemReady(itemType) {
    return this.cooldownSystem.isItemReady(itemType);
  }

  getItemCooldownProgress(itemType) {
    return this.cooldownSystem.getCooldownProgress(itemType);
  }

  getItemUses(itemType) {
    return this.upgradeSystem.getItemUses(itemType);
  }

  resetItemUses(itemType, uses) {
    return this.upgradeSystem.resetItemUses(itemType, uses);
  }

  addItemUses(itemType, amount = 1) {
    return this.upgradeSystem.addItemUses(itemType, amount);
  }

  // ===== 方块消除处理方法 =====

  /**
   * 处理方块移除和下落
   */
  _processBlockRemoval(affectedCols, removedPositions) {
    try {
      // 移除受影响的方块
      this._removeAffectedBlocks();

      // 处理方块下落
      if (affectedCols.size > 0) {
        const hasFallen = this._applyGravityToColumns(affectedCols, removedPositions);

        // 如果有方块下落，等待下落完成后再恢复游戏状态
        if (hasFallen) {
          setTimeout(() => {
            this._triggerMatchCheck();
            this._restoreGameState();
            this._clearProcessingFlag();
          }, 300);
        } else {
          this._triggerMatchCheck();
          this._restoreGameState();
          this._clearProcessingFlag();
        }
      } else {
        this._triggerMatchCheck();
        this._restoreGameState();
        this._clearProcessingFlag();
      }
    } catch (error) {
      console.error('完成道具效果时出错:', error);
      this._triggerMatchCheck();
      this._restoreGameState();
      this._clearProcessingFlag();
    }
  }

  /**
   * 移除所有受影响的方块
   */
  _removeAffectedBlocks() {
    for (const block of this.affectedBlocks) {
      if (block && typeof block.row === 'number' && typeof block.col === 'number') {
        // 移除网格中的方块
        this.grid.removeBlock(block.row, block.col);
      }
    }

    // 清空受影响的方块集合
    this.affectedBlocks.clear();
  }

  /**
   * 应用重力到指定列
   */
  _applyGravityToColumns(affectedCols, removedPositions) {
    try {
      const grid = this.dependencies.getGrid();
      if (!grid) {
        console.warn('无法获取网格对象');
        return false;
      }

      const columnsArray = Array.from(affectedCols);
      console.log('触发方块下落，影响列：', columnsArray);

      // 检查网格是否支持重力方法
      if (typeof grid.applyGravity === 'function') {
        return grid.applyGravity(columnsArray, null, removedPositions);
      } else if (typeof grid.applyFullGridGravity === 'function') {
        // 如果没有列级重力，使用全网格重力
        console.log('使用全网格重力作为备选方案');
        return grid.applyFullGridGravity();
      } else {
        console.warn('网格不支持重力应用方法');
        return false;
      }
    } catch (error) {
      console.error('应用重力时出错:', error);
      return false;
    }
  }

  /**
   * 触发消除检查
   */
  _triggerMatchCheck() {
    this.emit('check:matches');
  }
}