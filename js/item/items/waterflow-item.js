/**
 * 激流道具实现
 */
export default class WaterflowItem {
  constructor(grid, targetingSystem) {
    this.grid = grid;
    this.targetingSystem = targetingSystem;
  }

  use(row, col, level, callbacks) {
    console.log(`🌊 使用激流道具 等级${level}`);

    // 根据等级确定清除的行数（从底部开始）
    const rowCount = level;
    const affectedBlocks = [];
    const affectedRows = [];

    // 从底部开始清除指定行数
    for (let i = 0; i < rowCount; i++) {
      const targetRow = this.grid.rows - 1 - i;
      if (targetRow >= 0) {
        affectedRows.push(targetRow);
        for (let col = 0; col < this.grid.cols; col++) {
          const block = this.grid.getBlock(targetRow, col);
          if (block) {
            affectedBlocks.push({ row: targetRow, col });
            callbacks.markBlockAffected(block);
          }
        }
      }
    }

    if (affectedBlocks.length > 0) {
      // 创建激流效果，传递受影响的行
      callbacks.createWaterflowEffect(affectedRows, level);
      callbacks.playSound('激流.mp3');
      console.log(`🌊 激流清除了底部${rowCount}行，共${affectedBlocks.length}个方块`);
      return true;
    }

    console.log(`🌊 激流没有找到可清除的方块`);
    return false;
  }
}
