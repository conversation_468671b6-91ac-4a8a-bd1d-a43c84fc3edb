import Emitter from '../libs/tinyemitter.js';
import { SCREEN_WIDTH, SCREEN_HEIGHT } from '../render.js';
import { isDebugMode } from '../config/game-config.js';

// 加载资源
const atlas = wx.createImage();
atlas.src = 'images/Common.png';

// 按钮定义
const BUTTONS = {
  RESTART: {
    text: '重新开始',
    width: 120,
    height: 40
  },
  BACK: {
    text: '返回',
    width: 100,
    height: 40
  },
  LEVEL: {
    width: 100,
    height: 100,
    margin: 20
  },
  DIRECTION: {
    width: 60,
    height: 60
  },
  ITEM: {
    width: 70,
    height: 70
  }
};

// 星级显示配置
const STAR_CONFIG = {
  width: 30,
  height: 30,
  margin: 5
};

/**
 * 游戏信息显示类
 * 负责显示关卡选择、分数、时间、游戏状态等UI
 */
export default class GameInfo extends Emitter {
  constructor() {
    super();

    // 当前界面状态
    this.currentScreen = 'level'; // 'level', 'levelstart', 'game', 'gameover', 'levelcomplete', 'levelfail'
    
    // 关卡数据
    this.levels = [];
    this.currentLevel = null;
    this.targetScore = 0;
    
    // 防抖标志和计时器
    this.isProcessingTouch = false;
    this.lastTouchTime = 0;
    this.touchDebounceTime = 200; // 500ms防抖时间

    // 手势识别相关
    this.gestureStartX = 0;
    this.gestureStartY = 0;
    this.gestureStartTime = 0;
    this.isGestureTracking = false;
    this.gestureThreshold = 30; // 手势识别的最小移动距离
    this.gestureTimeThreshold = 500; // 手势识别的最大时间

    // 跟手移动相关
    this.isDragging = false; // 是否正在拖拽方块
    this.dragStartX = 0; // 拖拽开始的X坐标
    this.dragStartCol = 0; // 拖拽开始时方块的列位置
    this.isFingerOnScreen = false; // 手指是否还在屏幕上

    // 快速下落相关
    this.isFastDropping = false; // 是否正在快速下落
    this.isGestureFastDrop = false; // 是否是手势触发的快速下落
    this.isButtonPressed = false; // 是否有按钮被按下
    this.pressedButton = null; // 当前被按下的按钮
    
    // 轻击检测参数
    this.touchStartX = 0;
    this.touchStartY = 0;
    this.touchStartTime = 0;
    this.maxTapDistance = 15; // 最大轻击距离（像素）
    this.maxTapDuration = 300; // 最大轻击持续时间（毫秒）
    
    // 游戏操作按钮 - 方向键和旋转
    this.directionBtnAreas = {
      left: {
        startX: 20,
        startY: SCREEN_HEIGHT - 80,
        endX: 80,
        endY: SCREEN_HEIGHT - 20,
        text: '←'
      },
      down: {
        startX: 100,
        startY: SCREEN_HEIGHT - 80,
        endX: 160,
        endY: SCREEN_HEIGHT - 20,
        text: '↓'
      },
      right: {
        startX: 190,
        startY: SCREEN_HEIGHT - 80,
        endX: 250,
        endY: SCREEN_HEIGHT - 20,
        text: '→'
      },
      rotate: {
        startX: 300,
        startY: SCREEN_HEIGHT - 80,
        endX: 360,
        endY: SCREEN_HEIGHT - 20,
        text: '↻'
      }
    };
    
    // 记录当前按下的方向键
    this.activeDirection = null;
    
    // UI元素区域
    this.btnAreas = {
      restart: {
        startX: SCREEN_WIDTH / 2 - BUTTONS.RESTART.width / 2,
        startY: SCREEN_HEIGHT / 2 + 100,
        endX: SCREEN_WIDTH / 2 + BUTTONS.RESTART.width / 2,
        endY: SCREEN_HEIGHT / 2 + 100 + BUTTONS.RESTART.height
      },
      back: {
        startX: 20,
        startY: 20,
        endX: 20 + BUTTONS.BACK.width,
        endY: 20 + BUTTONS.BACK.height
      },
      levelStartGame: {
        startX: SCREEN_WIDTH / 2 - 80,
        startY: SCREEN_HEIGHT - 120,
        endX: SCREEN_WIDTH / 2 + 80,
        endY: SCREEN_HEIGHT - 70
      },
      levelStartBack: {
        startX: 50,
        startY: SCREEN_HEIGHT - 120,
        endX: 150,
        endY: SCREEN_HEIGHT - 70
      },
      levels: []
    };
    
    // 道具按钮区域
    this.itemBtnAreas = {
      fireball: {
        startX: SCREEN_WIDTH - 90,
        startY: SCREEN_HEIGHT / 2 - 140,
        endX: SCREEN_WIDTH - 20,
        endY: SCREEN_HEIGHT / 2 - 70,
        text: '火球术'
      },
      lightning: {
        startX: SCREEN_WIDTH - 90,
        startY: SCREEN_HEIGHT / 2 - 60,
        endX: SCREEN_WIDTH - 20,
        endY: SCREEN_HEIGHT / 2 + 10,
        text: '闪电链'
      },
      waterflow: {
        startX: SCREEN_WIDTH - 90,
        startY: SCREEN_HEIGHT / 2 + 20,
        endX: SCREEN_WIDTH - 20,
        endY: SCREEN_HEIGHT / 2 + 90,
        text: '激流'
      },
      earthquake: {
        startX: SCREEN_WIDTH - 90,
        startY: SCREEN_HEIGHT / 2 + 100,
        endX: SCREEN_WIDTH - 20,
        endY: SCREEN_HEIGHT / 2 + 170,
        text: '地震术'
      }
    };
    
    // 暂停按钮区域
    this.pauseArea = {
      startX: SCREEN_WIDTH - 90,
      startY: SCREEN_HEIGHT / 2 - 220,
      endX: SCREEN_WIDTH - 20,
      endY: SCREEN_HEIGHT / 2 - 150,
      text: '暂停'
    };
    
    // 游戏状态信息
    this.gameState = {
      score: 0,
      state: 'ready',
      levelInfo: null
    };
    
    // 游戏结果信息
    this.gameResult = {
      score: 0,
      stars: 0,
      timeElapsed: 0
    };

    // 目标信息
    this.targetInfo = null;

    // 滚动相关属性
    this.scrollOffset = 0; // 当前滚动偏移量
    this.maxScrollOffset = 0; // 最大滚动偏移量
    this.contentHeight = 0; // 内容总高度
    this.viewportHeight = SCREEN_HEIGHT - 120; // 可视区域高度（减去标题高度）
    this.isScrolling = false; // 是否正在滚动
    this.lastTouchY = 0; // 上次触摸Y坐标
    this.touchStartScrollY = 0; // 触摸开始时的滚动位置
    this.scrollVelocity = 0; // 滚动速度
    this.scrollDeceleration = 0.92; // 🎯 优化：稍微加快减速，让惯性滚动更自然
    this.minScrollVelocity = 1.0; // 🎯 优化：提高最小速度阈值，减少微小抖动

    // 新架构：触摸事件由TouchController统一处理，移除原有的微信API直接监听
    // wx.onTouchStart(this.touchEventHandler.bind(this));
    // wx.onTouchEnd(this.touchEndHandler.bind(this));
    // wx.onTouchMove(this.touchMoveHandler.bind(this));

    // 🔥 标记最后处理的按钮类型，避免重复处理
    this.lastProcessedButtonType = null;

    console.log('GameInfo: 触摸事件监听已恢复，将与main.js协同工作');
  }
  
  /**
   * 设置文本样式
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} size - 字体大小
   * @param {string} color - 字体颜色
   */
  setFont(ctx, size = 20, color = '#ffffff') {
    ctx.fillStyle = color;
    ctx.font = `${size}px Arial`;
  }
  
  /**
   * 显示关卡选择界面
   * @param {Array} stageData - 阶段数据
   */
  showLevelSelection(stageData) {
    this.currentScreen = 'level';
    this.stageData = stageData;

    // 重置触摸处理状态
    this.isProcessingTouch = false;
    this.lastTouchTime = 0;

    // 清理手势状态
    this.cleanupGestureStates();

    // 重置滚动状态
    this.scrollOffset = 0;
    this.scrollVelocity = 0;
    this.isScrolling = false;

    // 重新计算关卡按钮区域
    this.btnAreas.levels = [];
    this.btnAreas.stages = [];

    // 计算布局
    this._calculateStageLayout();

    // 计算滚动范围
    this._updateScrollBounds();
  }

  /**
   * 计算阶段和关卡的布局
   * @private
   */
  _calculateStageLayout() {
    let currentY = 120; // 从标题下方开始

    this.stageData.forEach((stage, stageIndex) => {
      // 阶段标题区域
      const stageHeaderHeight = 60;
      this.btnAreas.stages.push({
        stageNumber: stage.stageNumber,
        startX: 0,
        startY: currentY,
        endX: SCREEN_WIDTH,
        endY: currentY + stageHeaderHeight,
        isHeader: true
      });

      currentY += stageHeaderHeight + 10;

      // 关卡按钮布局 (每行3个)
      const levelsPerRow = 3;
      const levelButtonWidth = 80;
      const levelButtonHeight = 80;
      const levelButtonMargin = 20;

      const totalWidth = levelsPerRow * levelButtonWidth + (levelsPerRow - 1) * levelButtonMargin;
      const startX = (SCREEN_WIDTH - totalWidth) / 2;

      stage.levels.forEach((level, levelIndex) => {
        const row = Math.floor(levelIndex / levelsPerRow);
        const col = levelIndex % levelsPerRow;

        const x = startX + col * (levelButtonWidth + levelButtonMargin);
        const y = currentY + row * (levelButtonHeight + levelButtonMargin);

        this.btnAreas.levels.push({
          levelId: level.id,
          stageNumber: stage.stageNumber,
          startX: x,
          startY: y,
          endX: x + levelButtonWidth,
          endY: y + levelButtonHeight,
          level: level
        });
      });

      // 计算这个阶段占用的总高度
      const levelRows = Math.ceil(stage.levels.length / levelsPerRow);
      currentY += levelRows * (levelButtonHeight + levelButtonMargin) + 30; // 阶段间距
    });

    // 记录内容总高度
    this.contentHeight = currentY + 50; // 添加底部边距
  }

  /**
   * 更新滚动边界
   * @private
   */
  _updateScrollBounds() {
    // 计算最大滚动偏移量
    this.maxScrollOffset = Math.max(0, this.contentHeight - this.viewportHeight);

    // 确保当前滚动偏移量在有效范围内
    this.scrollOffset = Math.max(0, Math.min(this.scrollOffset, this.maxScrollOffset));
  }

  /**
   * 更新惯性滚动
   * @private
   */
  _updateInertialScrolling() {
    // 🎯 修复：只有在手指离开屏幕且有滚动速度时才应用惯性滚动
    if (!this.isFingerOnScreen && Math.abs(this.scrollVelocity) > this.minScrollVelocity) {
      this.scrollOffset += this.scrollVelocity;
      this.scrollVelocity *= this.scrollDeceleration;

      // 边界检查
      if (this.scrollOffset < 0) {
        this.scrollOffset = 0;
        this.scrollVelocity = 0;
        this.isScrolling = false;
      } else if (this.scrollOffset > this.maxScrollOffset) {
        this.scrollOffset = this.maxScrollOffset;
        this.scrollVelocity = 0;
        this.isScrolling = false;
      }
    } else if (Math.abs(this.scrollVelocity) <= this.minScrollVelocity) {
      this.scrollVelocity = 0;
      this.isScrolling = false;
    }
  }

  /**
   * 渲染滚动指示器
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @private
   */
  _renderScrollIndicator(ctx) {
    // 只有当内容超出视口时才显示滚动指示器
    if (this.maxScrollOffset <= 0) return;

    const indicatorWidth = 4;
    const indicatorX = SCREEN_WIDTH - indicatorWidth - 5;
    const indicatorStartY = 120;
    const indicatorHeight = this.viewportHeight;

    // 绘制滚动条背景
    ctx.fillStyle = 'rgba(255, 255, 255, 0.2)';
    ctx.fillRect(indicatorX, indicatorStartY, indicatorWidth, indicatorHeight);

    // 计算滚动条位置和大小
    const scrollRatio = this.scrollOffset / this.maxScrollOffset;
    const thumbHeight = Math.max(20, (this.viewportHeight / this.contentHeight) * indicatorHeight);
    const thumbY = indicatorStartY + scrollRatio * (indicatorHeight - thumbHeight);

    // 绘制滚动条
    ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
    ctx.fillRect(indicatorX, thumbY, indicatorWidth, thumbHeight);
  }
  
  /**
   * 显示游戏结束界面
   * @param {number} score - 最终得分
   */
  showGameOver(score) {
    this.currentScreen = 'gameover';
    this.gameResult.score = score;

    // 重置触摸处理状态
    this.isProcessingTouch = false;
    this.lastTouchTime = 0;

    // 清理手势状态
    this.cleanupGestureStates();
  }
  
  /**
   * 显示关卡完成界面
   * @param {number} score - 最终得分
   * @param {number} stars - 获得星级
   */
  showLevelComplete(score, stars) {
    this.currentScreen = 'levelcomplete';
    this.gameResult.score = score;
    this.gameResult.stars = stars;

    // 重置触摸处理状态
    this.isProcessingTouch = false;
    this.lastTouchTime = 0;

    // 清理手势状态
    this.cleanupGestureStates();
  }

  /**
   * 显示关卡失败界面
   * @param {string} reason - 失败原因
   */
  showLevelFail(reason) {
    this.currentScreen = 'levelfail';
    this.gameResult.reason = reason;

    // 重置触摸处理状态
    this.isProcessingTouch = false;
    this.lastTouchTime = 0;

    // 清理手势状态
    this.cleanupGestureStates();
  }
  
  /**
   * 显示关卡信息
   * @param {string} levelName - 关卡名称
   * @param {number} targetScore - 目标分数
   */
  showLevelInfo(levelName, targetScore) {
    this.currentScreen = 'game';
    this.currentLevel = levelName;
    this.targetScore = targetScore;

    // 重置触摸处理状态
    this.isProcessingTouch = false;
    this.lastTouchTime = 0;

    // 🔧 重要修复：游戏开始时清理所有手势状态
    this.cleanupGestureStates();
  }

  /**
   * 设置关卡目标信息
   * @param {Object} targetInfo - 目标信息
   * @param {number} targetInfo.targetScore - 目标分数
   * @param {Array} targetInfo.starThresholds - 星级阈值
   * @param {string} targetInfo.levelType - 关卡类型
   * @param {string} targetInfo.description - 关卡描述
   */
  setTargetInfo(targetInfo) {
    this.targetInfo = targetInfo;
  }

  /**
   * 显示关卡开始界面
   * @param {number} levelId - 关卡ID
   */
  showLevelStart(levelId) {
    this.currentScreen = 'levelstart';
    this.currentLevel = levelId;

    // 清理手势状态
    this.cleanupGestureStates();
    
    // 定义按钮区域（基于renderLevelStart中绘制的按钮位置）
    // 使用更灵活的计算方式，适应不同屏幕高度
    const buttonY = Math.max(SCREEN_HEIGHT - 120, 650); // 确保按钮不会太靠上
    
    // 开始游戏按钮区域 - 增加一些容错空间
    this.btnAreas.levelStartGame = {
      startX: SCREEN_WIDTH / 2 - 90,  // 增加宽度
      startY: buttonY - 10,           // 增加高度容错
      endX: SCREEN_WIDTH / 2 + 90,   // 增加宽度
      endY: buttonY + 60              // 增加高度容错
    };
    
    // 返回按钮区域 - 增加一些容错空间
    this.btnAreas.levelStartBack = {
      startX: 40,                     // 稍微扩大
      startY: buttonY - 10,           // 增加高度容错
      endX: 160,                      // 稍微扩大
      endY: buttonY + 60              // 增加高度容错
    };
    
    console.log('🎯 关卡开始界面按钮区域已定义');
    console.log('📍 开始游戏按钮区域:', this.btnAreas.levelStartGame);
    console.log('📍 返回按钮区域:', this.btnAreas.levelStartBack);
    console.log('📏 当前屏幕尺寸:', SCREEN_WIDTH, 'x', SCREEN_HEIGHT);
  }
  
  /**
   * 更新游戏信息状态
   */
  update() {
    // 在关卡选择界面时更新滚动
    if (this.currentScreen === 'level') {
      this._updateInertialScrolling();
    }
  }

  /**
   * 渲染游戏信息
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {Object} gameState - 游戏状态数据
   */
  render(ctx, gameState = {}) {
    // 更新游戏状态
    if (gameState) {
      this.gameState = { ...this.gameState, ...gameState };
    }

    // 根据当前界面状态渲染不同的UI
    switch (this.currentScreen) {
      case 'level':
        this.renderLevelSelection(ctx);
        break;
      case 'levelstart':
        this.renderLevelStart(ctx);
        break;
      case 'game':
        this.renderGameUI(ctx);
        break;
      case 'gameover':
        this.renderGameOver(ctx);
        break;
      case 'levelcomplete':
        this.renderLevelComplete(ctx);
        break;
      case 'levelfail':
        this.renderLevelFail(ctx);
        break;
    }
  }
  
  /**
   * 渲染关卡开始界面
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  renderLevelStart(ctx) {
    if (!this.targetInfo) return;

    // 绘制半透明背景
    ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
    ctx.fillRect(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT);

    // 绘制标题
    this.setFont(ctx, 32, '#ffffff');
    ctx.textAlign = 'center';
    ctx.fillText(this.targetInfo.description || `第${this.currentLevel}关`, SCREEN_WIDTH / 2, 100);

    // 绘制目标分数
    this.setFont(ctx, 24, '#ffff00');
    ctx.fillText(`目标分数: ${this.targetInfo.targetScore}`, SCREEN_WIDTH / 2, 150);

    // 绘制星级要求
    this.setFont(ctx, 18, '#ffffff');
    const starY = 200;
    ctx.fillText('星级要求:', SCREEN_WIDTH / 2, starY);

    if (this.targetInfo.starThresholds) {
      for (let i = 0; i < 3; i++) {
        const starX = SCREEN_WIDTH / 2 - 60 + i * 60;
        const threshold = this.targetInfo.starThresholds[i];

        // 绘制星星
        ctx.fillStyle = '#ffff00';
        this.drawStar(ctx, starX, starY + 30, 12);

        // 绘制分数
        this.setFont(ctx, 14, '#ffffff');
        ctx.textAlign = 'center';
        ctx.fillText(threshold.toString(), starX, starY + 60);
      }
    }

    // 绘制过关目标
    if (this.targetInfo.objectives && this.targetInfo.objectives.length > 0) {
      this.setFont(ctx, 16, '#ffffff');
      ctx.textAlign = 'left';
      ctx.fillText('过关目标:', 50, 300);

      this.targetInfo.objectives.forEach((objective, index) => {
        this.setFont(ctx, 14, '#cccccc');
        ctx.fillText(`• ${objective}`, 70, 330 + index * 25);
      });
    }

    // 绘制时间限制（如果有）
    if (this.targetInfo.timeLimit && this.targetInfo.timeLimit > 0) {
      this.setFont(ctx, 16, '#ff6666');
      ctx.textAlign = 'center';
      const minutes = Math.floor(this.targetInfo.timeLimit / 60);
      const seconds = this.targetInfo.timeLimit % 60;
      const timeText = `${minutes}:${seconds.toString().padStart(2, '0')}`;
      ctx.fillText(`⏰ 时间限制: ${timeText}`, SCREEN_WIDTH / 2, 450);
    }

    // 绘制开始按钮
    const buttonY = SCREEN_HEIGHT - 120;
    ctx.fillStyle = '#4CAF50';
    ctx.fillRect(SCREEN_WIDTH / 2 - 80, buttonY, 160, 50);

    this.setFont(ctx, 20, '#ffffff');
    ctx.textAlign = 'center';
    ctx.fillText('开始游戏', SCREEN_WIDTH / 2, buttonY + 32);

    // 绘制返回按钮
    ctx.fillStyle = '#757575';
    ctx.fillRect(50, buttonY, 100, 50);

    this.setFont(ctx, 16, '#ffffff');
    ctx.fillText('返回', 100, buttonY + 32);
  }

  /**
   * 渲染关卡选择界面
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  renderLevelSelection(ctx) {
    // 绘制背景
    ctx.fillStyle = '#1a1a1a';
    ctx.fillRect(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT);

    // 绘制标题（固定在顶部，不滚动）
    this.setFont(ctx, 36, '#ffffff');
    ctx.textAlign = 'center';
    ctx.fillText('关卡选择', SCREEN_WIDTH / 2, 60);

    // 更新惯性滚动
    this._updateInertialScrolling();

    // 设置裁剪区域（只显示可滚动内容区域）
    ctx.save();
    ctx.beginPath();
    ctx.rect(0, 120, SCREEN_WIDTH, this.viewportHeight);
    ctx.clip();

    // 应用滚动偏移
    ctx.translate(0, -this.scrollOffset);

    // 绘制各个阶段
    if (this.stageData) {
      this.stageData.forEach((stage, stageIndex) => {
        this._renderStage(ctx, stage, stageIndex);
      });
    }

    // 恢复画布状态
    ctx.restore();

    // 绘制滚动指示器
    this._renderScrollIndicator(ctx);

    // 重置文本对齐
    ctx.textAlign = 'left';
  }

  /**
   * 渲染单个阶段
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {Object} stage - 阶段数据
   * @param {number} stageIndex - 阶段索引
   * @private
   */
  _renderStage(ctx, stage, stageIndex) {
    const stageArea = this.btnAreas.stages[stageIndex];
    if (!stageArea) return;

    // 绘制阶段背景
    const gradient = ctx.createLinearGradient(0, stageArea.startY, 0, stageArea.endY);
    gradient.addColorStop(0, '#4a4a4a');
    gradient.addColorStop(1, '#2a2a2a');
    ctx.fillStyle = gradient;
    ctx.fillRect(stageArea.startX, stageArea.startY, SCREEN_WIDTH, stageArea.endY - stageArea.startY);

    // 绘制阶段标题
    this.setFont(ctx, 24, '#ffffff');
    ctx.textAlign = 'left';
    ctx.fillText(stage.stageName, 20, stageArea.startY + 30);

    // 绘制阶段描述
    this.setFont(ctx, 14, '#cccccc');
    ctx.fillText(stage.stageDescription, 20, stageArea.startY + 50);

    // 绘制阶段进度
    this.setFont(ctx, 16, '#ffff00');
    ctx.textAlign = 'right';
    const progressText = `${stage.totalStars}/${stage.maxStars} ⭐`;
    ctx.fillText(progressText, SCREEN_WIDTH - 20, stageArea.startY + 35);

    // 绘制关卡范围
    this.setFont(ctx, 12, '#999999');
    const rangeText = `${stage.stageRange.start}-${stage.stageRange.end === 999 ? '∞' : stage.stageRange.end}关`;
    ctx.fillText(rangeText, SCREEN_WIDTH - 20, stageArea.startY + 52);

    // 绘制关卡按钮
    stage.levels.forEach((level, levelIndex) => {
      this._renderLevelButton(ctx, level, stage.stageNumber);
    });
  }

  /**
   * 渲染关卡按钮
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {Object} level - 关卡数据
   * @param {number} stageNumber - 阶段编号
   * @private
   */
  _renderLevelButton(ctx, level, stageNumber) {
    const area = this.btnAreas.levels.find(area => area.levelId === level.id);
    if (!area) return;

    const buttonWidth = area.endX - area.startX;
    const buttonHeight = area.endY - area.startY;

    // 绘制按钮背景
    let bgColor = '#333333';
    if (level.stageInfo && level.stageInfo.isBossLevel) {
      bgColor = '#8B0000'; // Boss关卡用深红色
    } else if (level.stars > 0) {
      bgColor = '#2E7D32'; // 已完成关卡用绿色
    }

    ctx.fillStyle = bgColor;
    ctx.fillRect(area.startX, area.startY, buttonWidth, buttonHeight);

    // 绘制边框
    ctx.strokeStyle = '#666666';
    ctx.lineWidth = 2;
    ctx.strokeRect(area.startX, area.startY, buttonWidth, buttonHeight);

    // 绘制关卡编号
    this.setFont(ctx, 20, '#ffffff');
    ctx.textAlign = 'center';
    ctx.fillText(level.id.toString(), area.startX + buttonWidth / 2, area.startY + buttonHeight / 2 - 5);

    // 绘制关卡名称
    this.setFont(ctx, 10, '#cccccc');
    const levelName = level.name.length > 8 ? level.name.substring(0, 6) + '...' : level.name;
    ctx.fillText(levelName, area.startX + buttonWidth / 2, area.startY + buttonHeight / 2 + 15);

    // 绘制星级
    const stars = level.stars || 0;
    const starSize = 6;
    const starSpacing = 14;
    const totalStarWidth = 3 * starSpacing - starSpacing;
    const starStartX = area.startX + (buttonWidth - totalStarWidth) / 2;
    const starY = area.startY + buttonHeight - 15;

    for (let s = 0; s < 3; s++) {
      const starX = starStartX + s * starSpacing;

      ctx.fillStyle = s < stars ? '#FFD700' : '#444444';
      ctx.beginPath();
      ctx.arc(starX, starY, starSize, 0, Math.PI * 2);
      ctx.fill();
    }

    // Boss关卡标识
    if (level.stageInfo && level.stageInfo.isBossLevel) {
      this.setFont(ctx, 8, '#ffff00');
      ctx.fillText('BOSS', area.startX + buttonWidth / 2, area.startY + 12);
    }
  }

  /**
   * 渲染星级
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} stars - 星级数量
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   */
  renderStars(ctx, stars, x, y) {
    for (let i = 0; i < 3; i++) {
      // 绘制星星（实心或空心）
      ctx.fillStyle = i < stars ? '#FFD700' : '#777777';
      const starX = x + i * (STAR_CONFIG.width + STAR_CONFIG.margin);
      
      // 绘制星形
      this._drawStar(ctx, starX, y, STAR_CONFIG.width / 2, 5, 0.5);
    }
  }
  
  /**
   * 绘制星形
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} x - 中心X坐标
   * @param {number} y - 中心Y坐标
   * @param {number} radius - 半径
   * @param {number} points - 角数
   * @param {number} inset - 内缩比例
   */
  _drawStar(ctx, x, y, radius, points, inset) {
    ctx.beginPath();
    ctx.moveTo(x, y - radius);
    
    for (let i = 0; i < points * 2; i++) {
      const r = (i % 2 === 0) ? radius : radius * inset;
      const angle = (Math.PI * i) / points;
      ctx.lineTo(
        x + r * Math.sin(angle),
        y - r * Math.cos(angle)
      );
    }
    
    ctx.closePath();
    ctx.fill();
  }
  
  /**
   * 渲染游戏UI
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  renderGameUI(ctx) {
    // 绘制顶部信息栏背景
    ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    ctx.fillRect(0, 0, SCREEN_WIDTH, 100); // 增加高度以容纳更多信息

    // 绘制关卡名称
    this.setFont(ctx, 20, '#ffffff');
    ctx.textAlign = 'left';
    ctx.fillText(`第${this.currentLevel}关`, 20, 25);

    // 绘制当前得分
    ctx.textAlign = 'right';
    ctx.fillText(`得分: ${this.gameState.score}`, SCREEN_WIDTH - 20, 25);

    // 绘制目标信息
    this.renderTargetInfo(ctx);

    // 绘制时间信息（如果有时间限制）
    this.renderTimeInfo(ctx);

    // 重置文本对齐
    ctx.textAlign = 'left';

    // 渲染道具按钮
    this.renderItemButtons(ctx);

    // 渲染下一个方块区域
    this.renderPauseArea(ctx);

    // 渲染方向键
    this.renderDirectionButtons(ctx);

    // 如果游戏暂停，显示暂停界面
    if (this.gameState.state === 'paused') {
      this.renderPauseScreen(ctx);
    }

    // 渲染调试信息
    this.renderDebugInfo(ctx);
  }
  
  /**
   * 渲染目标信息
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  renderTargetInfo(ctx) {
    if (!this.targetInfo) return;

    const currentScore = this.gameState.score || 0;
    const targetScore = this.targetInfo.targetScore || 0;
    const starThresholds = this.targetInfo.starThresholds || [];

    // 绘制目标分数
    this.setFont(ctx, 16, '#ffff00');
    ctx.textAlign = 'left';
    ctx.fillText(`目标: ${targetScore}分`, 20, 50);

    // 绘制进度条背景
    const progressBarX = 20;
    const progressBarY = 55;
    const progressBarWidth = 200;
    const progressBarHeight = 8;

    ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
    ctx.fillRect(progressBarX, progressBarY, progressBarWidth, progressBarHeight);

    // 绘制进度条
    const progress = Math.min(currentScore / targetScore, 1);
    const progressWidth = progressBarWidth * progress;

    // 根据进度选择颜色
    let progressColor = '#ff4444'; // 红色（进度低）
    if (progress >= 0.5) progressColor = '#ffaa00'; // 橙色（进度中等）
    if (progress >= 0.8) progressColor = '#44ff44'; // 绿色（进度高）
    if (progress >= 1.0) progressColor = '#00ff00'; // 亮绿色（已完成）

    ctx.fillStyle = progressColor;
    ctx.fillRect(progressBarX, progressBarY, progressWidth, progressBarHeight);

    // 绘制星级指示器
    this.renderStarIndicators(ctx, currentScore, starThresholds, progressBarX + progressBarWidth + 10, progressBarY);

    // 绘制完成状态
    if (progress >= 1.0) {
      this.setFont(ctx, 14, '#00ff00');
      ctx.textAlign = 'left';
      ctx.fillText('✓ 目标达成！', progressBarX, progressBarY + progressBarHeight + 15);
    } else {
      // 显示还需要多少分
      const remaining = targetScore - currentScore;
      this.setFont(ctx, 12, '#ffffff');
      ctx.textAlign = 'left';
      ctx.fillText(`还需 ${remaining} 分`, progressBarX, progressBarY + progressBarHeight + 15);
    }
  }

  /**
   * 渲染星级指示器
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} currentScore - 当前分数
   * @param {Array} starThresholds - 星级阈值
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   */
  renderStarIndicators(ctx, currentScore, starThresholds, x, y) {
    const starSize = 16;
    const starSpacing = 20;

    for (let i = 0; i < 3; i++) {
      const starX = x + i * starSpacing;
      const starY = y - 2;

      // 判断星星是否点亮
      const threshold = starThresholds[i] || 0;
      const isLit = currentScore >= threshold;

      // 绘制星星
      ctx.fillStyle = isLit ? '#ffff00' : 'rgba(255, 255, 255, 0.3)';
      this.drawStar(ctx, starX, starY, starSize / 2);

      // 绘制阈值文本
      if (threshold > 0) {
        this.setFont(ctx, 10, isLit ? '#ffff00' : '#888888');
        ctx.textAlign = 'center';
        ctx.fillText(threshold.toString(), starX, starY + starSize + 8);
      }
    }
  }

  /**
   * 绘制星星形状
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @param {number} radius - 半径
   */
  drawStar(ctx, x, y, radius) {
    const spikes = 5;
    const outerRadius = radius;
    const innerRadius = radius * 0.4;

    ctx.beginPath();

    for (let i = 0; i < spikes * 2; i++) {
      const angle = (i * Math.PI) / spikes;
      const r = i % 2 === 0 ? outerRadius : innerRadius;
      const starX = x + Math.cos(angle) * r;
      const starY = y + Math.sin(angle) * r;

      if (i === 0) {
        ctx.moveTo(starX, starY);
      } else {
        ctx.lineTo(starX, starY);
      }
    }

    ctx.closePath();
    ctx.fill();
  }

  /**
   * 渲染时间信息
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  renderTimeInfo(ctx) {
    if (!this.gameState.levelInfo || !this.gameState.levelInfo.hasTimeLimit) {
      return;
    }

    const timeLeft = this.gameState.levelInfo.timeLeft;
    const timeElapsed = this.gameState.levelInfo.timeElapsed;

    // 绘制时间信息
    this.setFont(ctx, 16, timeLeft <= 30 ? '#ff4444' : '#ffffff');
    ctx.textAlign = 'right';

    if (timeLeft === Infinity) {
      ctx.fillText('无时间限制', SCREEN_WIDTH - 20, 50);
    } else {
      const minutes = Math.floor(timeLeft / 60);
      const seconds = timeLeft % 60;
      const timeText = `${minutes}:${seconds.toString().padStart(2, '0')}`;
      ctx.fillText(`剩余: ${timeText}`, SCREEN_WIDTH - 20, 50);

      // 如果时间紧急，添加警告效果
      if (timeLeft <= 30 && timeLeft > 0) {
        const alpha = Math.sin(Date.now() / 200) * 0.3 + 0.7; // 闪烁效果
        ctx.fillStyle = `rgba(255, 68, 68, ${alpha})`;
        ctx.fillText('⚠ 时间紧急！', SCREEN_WIDTH - 20, 70);
      }
    }
  }

  /**
   * 渲染暂停按钮区域
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  renderPauseArea(ctx) {
    const area = this.pauseArea;
    
    // 绘制背景
    ctx.fillStyle = 'rgba(255, 255, 255, 0.2)';
    ctx.strokeStyle = '#ffffff';
    ctx.lineWidth = 2;
    
    // 绘制圆角矩形
    const radius = 15;
    ctx.beginPath();
    ctx.moveTo(area.startX + radius, area.startY);
    ctx.lineTo(area.endX - radius, area.startY);
    ctx.arcTo(area.endX, area.startY, area.endX, area.startY + radius, radius);
    ctx.lineTo(area.endX, area.endY - radius);
    ctx.arcTo(area.endX, area.endY, area.endX - radius, area.endY, radius);
    ctx.lineTo(area.startX + radius, area.endY);
    ctx.arcTo(area.startX, area.endY, area.startX, area.endY - radius, radius);
    ctx.lineTo(area.startX, area.startY + radius);
    ctx.arcTo(area.startX, area.startY, area.startX + radius, area.startY, radius);
    ctx.closePath();
    
    ctx.fill();
    ctx.stroke();
    
    // 绘制文本
    this.setFont(ctx, 18, '#000000');
    ctx.textAlign = 'center';
    ctx.fillText(area.text, (area.startX + area.endX) / 2, area.startY + 25);
    
    // 重置文本对齐
    ctx.textAlign = 'left';
  }
  
  /**
   * 渲染道具按钮
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  renderItemButtons(ctx) {
    // 获取道具管理器的引用
    const itemManager = GameGlobal.main && GameGlobal.main.itemManager;

    // 遍历所有道具按钮
    Object.entries(this.itemBtnAreas).forEach(([itemType, area]) => {
      // 检查道具是否已解锁
      if (itemManager && !itemManager.isItemUnlocked(itemType)) {
        // 未解锁的道具不显示
        return;
      }

      // 获取道具信息
      let itemInfo = null;
      if (itemManager) {
        itemInfo = itemManager.getItemInfo(itemType);
      }

      // 渲染按钮
      this.renderItemButton(ctx, area, area.text, itemType, itemInfo);
    });
  }
  
  /**
   * 渲染单个道具按钮
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {Object} area - 按钮区域
   * @param {string} text - 按钮文本
   * @param {string} itemType - 道具类型
   * @param {Object} itemInfo - 道具信息
   */
  renderItemButton(ctx, area, text, itemType, itemInfo) {
    // 检查是否激活（按下状态）
    const isActive = area.isActive;
    
    // 按钮状态判断
    const isDisabled = itemInfo && (!itemInfo.hasUses || !itemInfo.isReady);
    
    // 获取按钮宽高
    const width = area.endX - area.startX;
    const height = area.endY - area.startY;
    
    // 绘制背景
    if (isDisabled) {
      ctx.fillStyle = 'rgba(100, 100, 100, 0.5)'; // 灰色（禁用）
    } else if (isActive) {
      ctx.fillStyle = 'rgba(255, 200, 0, 0.5)'; // 金色（激活）
    } else {
      ctx.fillStyle = 'rgba(255, 255, 255, 0.3)'; // 半透明白色（正常）
    }
    
    ctx.strokeStyle = isDisabled ? '#555555' : '#ffffff';
    ctx.lineWidth = 2;
    
    // 绘制圆角矩形
    const radius = 15;
    ctx.beginPath();
    ctx.moveTo(area.startX + radius, area.startY);
    ctx.lineTo(area.endX - radius, area.startY);
    ctx.arcTo(area.endX, area.startY, area.endX, area.startY + radius, radius);
    ctx.lineTo(area.endX, area.endY - radius);
    ctx.arcTo(area.endX, area.endY, area.endX - radius, area.endY, radius);
    ctx.lineTo(area.startX + radius, area.endY);
    ctx.arcTo(area.startX, area.endY, area.startX, area.endY - radius, radius);
    ctx.lineTo(area.startX, area.startY + radius);
    ctx.arcTo(area.startX, area.startY, area.startX + radius, area.startY, radius);
    ctx.closePath();
    
    ctx.fill();
    ctx.stroke();
    
    // 根据道具类型绘制不同的背景图片
    if (itemType === 'fireball') {
      // 检查图片是否已加载
      if (!this.fireballImage) {
        this.fireballImage = wx.createImage();
        this.fireballImage.src = 'images/fireball.jpg';
      }
      
      // 如果图片已加载，绘制图片
      if (this.fireballImage.complete) {
        // 在矩形内居中绘制图片
        const padding = 5;
        const imgSize = Math.min(width, height) - padding * 2;
        
        ctx.save();
        // 创建圆形裁剪区域
        ctx.beginPath();
        ctx.arc(
          (area.startX + area.endX) / 2, 
          (area.startY + area.endY) / 2, 
          imgSize / 2, 
          0, 
          Math.PI * 2
        );
        ctx.clip();
        
        // 绘制图片
        ctx.drawImage(
          this.fireballImage,
          (area.startX + area.endX) / 2 - imgSize / 2,
          (area.startY + area.endY) / 2 - imgSize / 2,
          imgSize,
          imgSize
        );
        ctx.restore();
      }
    } else if (itemType === 'lightning') {
      // 检查闪电链图片是否已加载
      if (!this.lightningImage) {
        this.lightningImage = wx.createImage();
        this.lightningImage.src = 'images/lightning.jpg';
        this.lightningImage.onload = () => {
          console.log('闪电链图片加载成功');
        };
        this.lightningImage.onerror = (e) => {
          console.error('闪电链图片加载失败:', e);
        };
      }
      
      // 如果图片已加载，绘制图片
      if (this.lightningImage && this.lightningImage.complete) {
        // 在矩形内居中绘制图片
        const padding = 5;
        const imgSize = Math.min(width, height) - padding * 2;
        
        ctx.save();
        // 创建圆形裁剪区域
        ctx.beginPath();
        ctx.arc(
          (area.startX + area.endX) / 2, 
          (area.startY + area.endY) / 2, 
          imgSize / 2, 
          0, 
          Math.PI * 2
        );
        ctx.clip();
        
        // 绘制图片
        ctx.drawImage(
          this.lightningImage,
          (area.startX + area.endX) / 2 - imgSize / 2,
          (area.startY + area.endY) / 2 - imgSize / 2,
          imgSize,
          imgSize
        );
        ctx.restore();
      }
    }

    if (itemType === 'waterflow') {
      // 检查激流图片是否已加载
      if (!this.waterflowImage) {
        this.waterflowImage = wx.createImage();
        this.waterflowImage.src = 'images/water.jpg';
        this.waterflowImage.onload = () => {
          console.log('激流图片加载成功');
        };
        this.waterflowImage.onerror = (e) => {
          console.error('激流图片加载失败:', e);
        };
      }
      
      // 如果图片已加载，绘制图片
      if (this.waterflowImage && this.waterflowImage.complete) {
        // 在矩形内居中绘制图片
        const padding = 5;
        const imgSize = Math.min(width, height) - padding * 2;
        
        ctx.save();
        // 创建圆形裁剪区域
        ctx.beginPath();
        ctx.arc(
          (area.startX + area.endX) / 2, 
          (area.startY + area.endY) / 2, 
          imgSize / 2, 
          0, 
          Math.PI * 2
        );
        ctx.clip();
        
        // 绘制图片
        ctx.drawImage(
          this.waterflowImage,
          (area.startX + area.endX) / 2 - imgSize / 2,
          (area.startY + area.endY) / 2 - imgSize / 2,
          imgSize,
          imgSize
        );
        ctx.restore();
      }
    }

    if (itemType === 'earthquake') {
      // 检查地震术图片是否已加载
      if (!this.earthquakeImage) {
        this.earthquakeImage = wx.createImage();
        this.earthquakeImage.src = 'images/earthquake.jpg';
        this.earthquakeImage.onload = () => {
          console.log('地震术图片加载成功');
        };
        this.earthquakeImage.onerror = (e) => {
          console.error('地震术图片加载失败:', e);
        };
      }
      
      // 如果图片已加载，绘制图片
      if (this.earthquakeImage && this.earthquakeImage.complete) {
        // 在矩形内居中绘制图片
        const padding = 5;
        const imgSize = Math.min(width, height) - padding * 2;
        
        ctx.save();
        // 创建圆形裁剪区域
        ctx.beginPath();
        ctx.arc(
          (area.startX + area.endX) / 2, 
          (area.startY + area.endY) / 2, 
          imgSize / 2, 
          0, 
          Math.PI * 2
        );
        ctx.clip();
        
        // 绘制图片
        ctx.drawImage(
          this.earthquakeImage,
          (area.startX + area.endX) / 2 - imgSize / 2,
          (area.startY + area.endY) / 2 - imgSize / 2,
          imgSize,
          imgSize
        );
        ctx.restore();
      }
    }
    
    // 绘制文本
    ctx.textAlign = 'center';
    
    // 绘制道具名称
    this.setFont(ctx, 14, isDisabled ? '#888888' : '#ffffff');
    ctx.fillText(text, (area.startX + area.endX) / 2, area.startY + 15);
    
    // 如果有道具信息，显示等级和次数
    if (itemInfo) {
      // 绘制等级
      this.setFont(ctx, 16, isDisabled ? '#888888' : '#ffff00');
      ctx.fillText(`Lv.${itemInfo.level}`, (area.startX + area.endX) / 2, (area.startY + area.endY) / 2 + 5);
      
      // 地震术显示影响范围
      if (itemType === 'earthquake') {
        const affectedRows = 2 + itemInfo.level; // 根据等级计算影响行数
        this.setFont(ctx, 12, isDisabled ? '#666666' : '#ffaa00');
        ctx.fillText(`底部${affectedRows}行`, (area.startX + area.endX) / 2, (area.startY + area.endY) / 2 + 20);
      }
      
      // 绘制次数（调试模式下显示∞）
      this.setFont(ctx, 16, isDisabled ? '#888888' : '#ffffff');
      const usesText = (isDebugMode() && itemInfo.uses >= 999) ? '∞次' : `${itemInfo.uses}次`;
      ctx.fillText(usesText, (area.startX + area.endX) / 2, area.endY - 10);
      
      // 如果在冷却中，绘制冷却进度（调试模式下跳过）
      if (itemInfo.cooldown > 0 && (!isDebugMode() || itemInfo.cooldown > 0)) {
        const cooldownProgress = itemInfo.cooldown / itemInfo.maxCooldown;
        const cooldownHeight = height * cooldownProgress;
        
        // 绘制冷却遮罩
        ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
        ctx.fillRect(
          area.startX,
          area.startY + height - cooldownHeight,
          width,
          cooldownHeight
        );
      }
    }
    
    // 重置文本对齐
    ctx.textAlign = 'left';
  }
  
  /**
   * 渲染方向按钮
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  renderDirectionButtons(ctx) {
    // 遍历所有方向按钮
    Object.values(this.directionBtnAreas).forEach(area => {
      // 绘制背景
      ctx.fillStyle = 'rgba(255, 255, 255, 0.2)';
      ctx.strokeStyle = '#ffffff';
      ctx.lineWidth = 2;
      
      // 绘制圆角矩形
      const radius = 15;
      ctx.beginPath();
      ctx.moveTo(area.startX + radius, area.startY);
      ctx.lineTo(area.endX - radius, area.startY);
      ctx.arcTo(area.endX, area.startY, area.endX, area.startY + radius, radius);
      ctx.lineTo(area.endX, area.endY - radius);
      ctx.arcTo(area.endX, area.endY, area.endX - radius, area.endY, radius);
      ctx.lineTo(area.startX + radius, area.endY);
      ctx.arcTo(area.startX, area.endY, area.startX, area.endY - radius, radius);
      ctx.lineTo(area.startX, area.startY + radius);
      ctx.arcTo(area.startX, area.startY, area.startX + radius, area.startY, radius);
      ctx.closePath();
      
      ctx.fill();
      ctx.stroke();
      
      // 绘制文本
      this.setFont(ctx, 24, '#000000');
      ctx.textAlign = 'center';
      ctx.fillText(area.text, (area.startX + area.endX) / 2, (area.startY + area.endY) / 2 + 8);
    });
    
    // 重置文本对齐
    ctx.textAlign = 'left';
  }
  
  /**
   * 渲染暂停界面
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  renderPauseScreen(ctx) {
    // 半透明背景
    ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    ctx.fillRect(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT);
    
    // 暂停文本
    this.setFont(ctx, 36, '#ffffff');
    const pauseText = '游戏暂停';
    ctx.textAlign = 'center';
    ctx.fillText(pauseText, SCREEN_WIDTH / 2, SCREEN_HEIGHT / 2 - 50);
    
    // 继续提示
    this.setFont(ctx, 24, '#ffffff');
    ctx.fillText('点击屏幕继续', SCREEN_WIDTH / 2, SCREEN_HEIGHT / 2 + 20);
    
    // 重置文本对齐
    ctx.textAlign = 'left';
  }
  
  /**
   * 渲染游戏结束界面
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  renderGameOver(ctx) {
    // 半透明背景
    ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
    ctx.fillRect(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT);
    
    // 游戏结束标题
    this.setFont(ctx, 36, '#ffffff');
    const gameOverText = '游戏结束';
    const textWidth = ctx.measureText(gameOverText).width;
    ctx.fillText(gameOverText, (SCREEN_WIDTH - textWidth) / 2, SCREEN_HEIGHT / 2 - 80);
    
    // 分数
    this.setFont(ctx, 30, '#ffffff');
    const scoreText = `最终得分: ${this.gameResult.score}`;
    const scoreWidth = ctx.measureText(scoreText).width;
    ctx.fillText(scoreText, (SCREEN_WIDTH - scoreWidth) / 2, SCREEN_HEIGHT / 2 - 20);
    
    // 重新开始按钮
    this.renderButton(ctx, this.btnAreas.restart, BUTTONS.RESTART.text);
  }
  
  /**
   * 渲染关卡完成界面
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  renderLevelComplete(ctx) {
    // 半透明背景
    ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
    ctx.fillRect(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT);
    
    // 关卡完成标题
    this.setFont(ctx, 36, '#ffffff');
    const completeText = '关卡完成！';
    const textWidth = ctx.measureText(completeText).width;
    ctx.fillText(completeText, (SCREEN_WIDTH - textWidth) / 2, SCREEN_HEIGHT / 2 - 100);
    
    // 分数
    this.setFont(ctx, 30, '#ffffff');
    const scoreText = `得分: ${this.gameResult.score}`;
    const scoreWidth = ctx.measureText(scoreText).width;
    ctx.fillText(scoreText, (SCREEN_WIDTH - scoreWidth) / 2, SCREEN_HEIGHT / 2 - 40);
    
    // 绘制星级
    const starsX = (SCREEN_WIDTH - (STAR_CONFIG.width * 3 + STAR_CONFIG.margin * 2) * 2) / 2;
    this.renderStars(ctx, this.gameResult.stars, starsX, SCREEN_HEIGHT / 2);
    
    // 返回按钮
    this.renderButton(ctx, this.btnAreas.restart, '下一关');
  }
  
  /**
   * 渲染关卡失败界面
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  renderLevelFail(ctx) {
    // 半透明背景
    ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
    ctx.fillRect(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT);
    
    // 关卡失败标题
    this.setFont(ctx, 36, '#ffffff');
    const failText = '关卡失败';
    const textWidth = ctx.measureText(failText).width;
    ctx.fillText(failText, (SCREEN_WIDTH - textWidth) / 2, SCREEN_HEIGHT / 2 - 80);
    
    // 失败原因
    this.setFont(ctx, 24, '#ffffff');
    const reasonText = `原因: ${this.gameResult.reason || ''}`;
    const reasonWidth = ctx.measureText(reasonText).width;
    ctx.fillText(reasonText, (SCREEN_WIDTH - reasonWidth) / 2, SCREEN_HEIGHT / 2 - 20);
    
    // 重新开始按钮
    this.renderButton(ctx, this.btnAreas.restart, BUTTONS.RESTART.text);
  }
  
  /**
   * 渲染按钮
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {Object} area - 按钮区域
   * @param {string} text - 按钮文本
   */
  renderButton(ctx, area, text) {
    // 绘制按钮背景
    ctx.fillStyle = '#333333';
    ctx.fillRect(area.startX, area.startY, area.endX - area.startX, area.endY - area.startY);
    
    // 绘制按钮边框
    ctx.strokeStyle = '#ffffff';
    ctx.lineWidth = 2;
    ctx.strokeRect(area.startX, area.startY, area.endX - area.startX, area.endY - area.startY);
    
    // 绘制按钮文本
    this.setFont(ctx, 20, '#ffffff');
    const textWidth = ctx.measureText(text).width;
    ctx.fillText(
      text,
      area.startX + ((area.endX - area.startX) - textWidth) / 2,
      area.startY + ((area.endY - area.startY) + 15) / 2
    );
  }
  
  /**
   * 处理触摸事件
   * @param {Object} event - 触摸事件对象
   */
  touchEventHandler(event) {
    const now = Date.now();

    // 🔍 添加更严格的重复处理检查
    if (GameGlobal.main && GameGlobal.main.pendingTouch) {
      console.log(`🚫 [${now}] GameInfo: main.js正在处理触摸，忽略重复事件`);
      return;
    }

    // 检查是否是暂停状态（暂停状态下允许触摸，用于继续游戏）
    const isPaused = GameGlobal.main && GameGlobal.main.gameController &&
                     GameGlobal.main.gameController.state === 'paused';

    if (!isPaused) {
      // 非暂停状态下检查主程序的触摸禁用状态
      if (GameGlobal.main && GameGlobal.main.touchDisabled) {
        console.log(`🚫 [${now}] GameInfo: 主程序触摸已禁用，忽略触摸`);
        return;
      }

      // 检查主程序的过渡状态
      if (GameGlobal.main && GameGlobal.main.isTransitioning) {
        console.log(`⏳ [${now}] GameInfo: 主程序正在过渡，忽略触摸`);
        return;
      }
    }

    // 检查是否有新元素介绍正在显示
    if (GameGlobal.main && GameGlobal.main.elementIntroduction &&
        GameGlobal.main.elementIntroduction.isShowing) {
      console.log(`📚 [${now}] GameInfo: 新元素介绍正在显示，忽略触摸`);
      return;
    }

    // 防抖处理，避免快速连续触发
    if (this.isProcessingTouch && (now - this.lastTouchTime) < this.touchDebounceTime) {
      console.log(`⏰ [${now}] GameInfo: 触摸防抖，忽略事件`);
      return;
    }

    this.isProcessingTouch = true;
    this.lastTouchTime = now;

    // 调试触摸事件结构
    console.log(`🔍 [${now}] 触摸事件调试:`, {
      hasEvent: !!event,
      hasTouches: !!(event && event.touches),
      touchesLength: event && event.touches ? event.touches.length : 0,
      firstTouch: event && event.touches && event.touches[0] ? {
        clientX: event.touches[0].clientX,
        clientY: event.touches[0].clientY,
        pageX: event.touches[0].pageX,
        pageY: event.touches[0].pageY
      } : null
    });

    // 安全获取触摸坐标
    let x, y;
    if (event && event.touches && event.touches.length > 0) {
      const touch = event.touches[0];
      // 优先使用pageX/pageY，如果不存在则使用clientX/clientY
      x = touch.pageX !== undefined ? touch.pageX : touch.clientX;
      y = touch.pageY !== undefined ? touch.pageY : touch.clientY;
    } else {
      console.log(`❌ [${now}] 无效的触摸事件结构`);
      this.isProcessingTouch = false;
      return;
    }

    // 验证坐标有效性
    if (x === undefined || y === undefined || isNaN(x) || isNaN(y)) {
      this.isProcessingTouch = false;
      return;
    }

    console.log(`✅ [${now}] 有效触摸坐标: (${x}, ${y})`);

    // 记录触摸开始时间和位置（用于轻击检测）
    this.touchStartX = x;
    this.touchStartY = y;
    this.touchStartTime = now;

    // 关卡选择界面的滚动处理
    if (this.currentScreen === 'level') {
      this.lastTouchY = y;
      this.touchStartScrollY = this.scrollOffset;
      this.scrollVelocity = 0; // 停止惯性滚动
      this.isScrolling = false;
      this.isFingerOnScreen = true; // 🎯 修复：标记手指在屏幕上

      // 关卡选择界面只处理滚动，不立即处理点击
      // 点击检测在touchEnd中进行
    } else {
      // 其他界面立即处理触摸事件
      switch (this.currentScreen) {
        case 'levelstart':
          this.handleLevelStartTouch(x, y);
          break;
        case 'game':
          console.log(`🔄 [${now}] 准备调用 handleGameScreenTouch，参数: (${x}, ${y})`);
          // 记录手势起始位置
          this.gestureStartX = x;
          this.gestureStartY = y;
          this.gestureStartTime = now;
          this.isGestureTracking = true;
          this.isFingerOnScreen = true; // 标记手指在屏幕上
          this.handleGameScreenTouch(x, y);
          break;
        case 'gameover':
        case 'levelcomplete':
        case 'levelfail':
          this.handleResultScreenTouch(x, y);
          break;
      }
    }

    // 处理完成后重置标志
    setTimeout(() => {
      this.isProcessingTouch = false;
    }, 100);
  }
  
  /**
   * 处理触摸移动事件
   * @param {Object} event - 触摸事件对象
   */
  touchMoveHandler(event) {
    // 检查主程序的触摸禁用状态
    if (GameGlobal.main && GameGlobal.main.touchDisabled) {
      return;
    }

    // 检查主程序的过渡状态
    if (GameGlobal.main && GameGlobal.main.isTransitioning) {
      return;
    }

    // 安全获取触摸坐标
    if (!event || !event.touches || event.touches.length === 0) {
      return;
    }

    const touch = event.touches[0];
    const x = touch.pageX !== undefined ? touch.pageX : touch.clientX;
    const y = touch.pageY !== undefined ? touch.pageY : touch.clientY;

    if (x === undefined || y === undefined || isNaN(x) || isNaN(y)) {
      return;
    }

    // 处理关卡选择界面的滚动
    if (this.currentScreen === 'level') {
      // 计算滚动距离
      const deltaY = this.lastTouchY - y;
      this.lastTouchY = y;

      // 只有当移动距离足够大时才开始滚动
      if (Math.abs(deltaY) > 1) {
        // 🎯 修复：确保1:1跟手滚动，停止惯性滚动
        this.scrollVelocity = 0; // 手指移动时停止惯性滚动

        // 更新滚动偏移量（1:1跟手）
        const newScrollOffset = this.scrollOffset + deltaY;
        this.scrollOffset = Math.max(0, Math.min(newScrollOffset, this.maxScrollOffset));

        // 标记为正在滚动
        this.isScrolling = true;

        // 🎯 修复：记录滚动速度用于惯性滚动（不放大）
        this.scrollVelocity = deltaY;
      }
      return;
    }

    // 处理游戏界面的手势识别和跟手移动
    if (this.currentScreen === 'game') {
      // 如果已经触发了快速下落，不再处理手势
      if (this.isGestureTracking && !this.isFastDropping) {
        this.handleGameGesture(x, y);
      }

      // 处理跟手拖拽移动
      if (this.isDragging) {
        this.handleDragMove(x);
      }
    }
  }

  /**
   * 处理游戏中的手势识别
   * @param {number} x - 当前触摸X坐标
   * @param {number} y - 当前触摸Y坐标
   */
  handleGameGesture(x, y) {
    if (!this.isGestureTracking) {
      return;
    }

    const now = Date.now();
    const deltaX = x - this.gestureStartX;
    const deltaY = y - this.gestureStartY;
    const timeDelta = now - this.gestureStartTime;

    // 检查是否超过时间阈值
    if (timeDelta > this.gestureTimeThreshold) {
      this.isGestureTracking = false;
      return;
    }

    // 水平手势检测 - 跟手拖拽模式
    if (Math.abs(deltaX) > this.gestureThreshold && Math.abs(deltaX) > Math.abs(deltaY)) {
      if (!this.isDragging) {
        this.startDragging(x);
        // 不关闭手势跟踪，继续跟踪手指移动
      }
      return;
    }

    // 垂直手势检测（向下快速下落）
    if (deltaY > this.gestureThreshold && Math.abs(deltaY) > Math.abs(deltaX)) {
      if (!this.isFastDropping) {
        this.isFastDropping = true;
        this.isGestureFastDrop = true;

        // 发送快速下落事件
        this.emit('move', { direction: 'down', pressed: true });

        // 直接同步状态到游戏控制器
        const gameController = GameGlobal.main && GameGlobal.main.gameController;
        if (gameController) {
          gameController.isSoftDropping = true;
        }

        // 关闭手势跟踪，避免重复触发快速下落
        this.isGestureTracking = false;
      }
      return;
    }

    // 向上手势检测（旋转）
    if (deltaY < -this.gestureThreshold && Math.abs(deltaY) > Math.abs(deltaX)) {
      this.emit('rotate');
      this.isGestureTracking = false; // 旋转是一次性动作，可以结束跟踪
      return;
    }
  }

  /**
   * 开始跟手拖拽
   * @param {number} x - 当前触摸X坐标
   */
  startDragging(x) {
    // console.log(`🎮 [${Date.now()}] 开始跟手拖拽，起始X坐标: ${x}`);

    this.isDragging = true;
    this.dragStartX = x;

    // 获取当前方块的列位置
    const gameController = GameGlobal.main && GameGlobal.main.gameController;
    if (gameController && gameController.currentTetromino) {
      this.dragStartCol = gameController.currentTetromino.position.col;
      // console.log(`📍 记录起始列位置: ${this.dragStartCol}`);
    }
  }

  /**
   * 处理跟手拖拽移动
   * @param {number} x - 当前触摸X坐标
   */
  handleDragMove(x) {
    if (!this.isDragging) return;

    const gameController = GameGlobal.main && GameGlobal.main.gameController;
    if (!gameController || !gameController.currentTetromino || !gameController.grid) {
      return;
    }

    // 计算手指移动的距离
    const deltaX = x - this.dragStartX;

    // 将像素距离转换为网格列数
    const blockSize = gameController.grid.blockSize;
    const deltaCol = Math.round(deltaX / blockSize);

    // 计算目标列位置
    const targetCol = this.dragStartCol + deltaCol;

    // 设置方块位置（带边界检查）
    this.setTetrominoPosition(targetCol);
  }

  /**
   * 停止跟手拖拽
   */
  stopDragging() {
    // console.log(`🛑 [${Date.now()}] 停止跟手拖拽`);
    this.isDragging = false;
    this.dragStartX = 0;
    this.dragStartCol = 0;
  }

  /**
   * 直接设置方块位置
   * @param {number} targetCol - 目标列位置
   */
  setTetrominoPosition(targetCol) {
    const gameController = GameGlobal.main && GameGlobal.main.gameController;
    if (!gameController || !gameController.currentTetromino || !gameController.grid) {
      return;
    }

    const tetromino = gameController.currentTetromino;
    const grid = gameController.grid;

    // 保存原始位置
    const originalCol = tetromino.position.col;

    // 设置新位置
    tetromino.position.col = targetCol;

    // 检查新位置是否有效
    if (!tetromino.isValidPosition(grid)) {
      // 如果无效，恢复原始位置
      tetromino.position.col = originalCol;
      return;
    }

    // 位置有效，重置锁定计时器（如果可能）
    if (gameController._resetLockTimerIfPossible) {
      gameController._resetLockTimerIfPossible();
    }

    // 🎵 添加移动音效：只有当位置真正发生变化时才播放音效
    if (originalCol !== targetCol && GameGlobal.musicManager) {
      GameGlobal.musicManager.playMove();
    }
  }

  /**
   * 停止快速下落
   */
  stopFastDrop() {
    if (this.isFastDropping) {
      // 发送停止快速下落事件
      this.emit('move', { direction: 'down', pressed: false });

      // 重置状态
      this.isFastDropping = false;
      this.isGestureFastDrop = false;

      // 同步状态到游戏控制器
      const gameController = GameGlobal.main && GameGlobal.main.gameController;
      if (gameController) {
        gameController.isSoftDropping = false;
      }
    }
  }

  /**
   * 清理所有手势状态
   */
  cleanupGestureStates() {
    this.isFingerOnScreen = false;
    this.stopDragging();
    this.stopFastDrop();
    this.isGestureTracking = false;

    // 清理按钮状态
    this.isButtonPressed = false;
    this.pressedButton = null;
    this.activeDirection = null;

    // 清理手势快速下落状态
    this.isGestureFastDrop = false;

    // 强制重置游戏控制器的快速下落状态
    const gameController = GameGlobal.main && GameGlobal.main.gameController;
    if (gameController) {
      gameController.isSoftDropping = false;
    }
  }

  /**
   * 处理触摸结束事件
   * @param {Object} event - 触摸事件对象
   */
  touchEndHandler(event) {
    const now = Date.now();

    // 检查是否是暂停状态（暂停状态下允许触摸，用于继续游戏）
    const isPaused = GameGlobal.main && GameGlobal.main.gameController &&
                     GameGlobal.main.gameController.state === 'paused';

    if (!isPaused) {
      // 非暂停状态下检查主程序的触摸禁用状态
      if (GameGlobal.main && GameGlobal.main.touchDisabled) {
        // console.log(`🚫 [${now}] GameInfo: 主程序触摸已禁用，忽略touchEnd`);
        return;
      }

      // 检查主程序的过渡状态
      if (GameGlobal.main && GameGlobal.main.isTransitioning) {
        // console.log(`⏳ [${now}] GameInfo: 主程序正在过渡，忽略touchEnd`);
        return;
      }
    } else {
      // console.log(`⏸️ [${now}] GameInfo: 暂停状态，允许touchEnd用于继续游戏`);
    }

    // 关卡选择界面的滚动处理
    if (this.currentScreen === 'level') {
      // 检查触摸开始数据有效性
      if (this.touchStartX === undefined || this.touchStartY === undefined ||
          isNaN(this.touchStartX) || isNaN(this.touchStartY)) {
        return;
      }

      const touchDuration = now - this.touchStartTime;

      // 获取触摸结束位置
      let endX = this.touchStartX;
      let endY = this.touchStartY;

      if (event.changedTouches && event.changedTouches.length > 0) {
        const touch = event.changedTouches[0];
        endX = touch.pageX !== undefined ? touch.pageX : touch.clientX;
        endY = touch.pageY !== undefined ? touch.pageY : touch.clientY;

        // 检查触摸结束坐标有效性
        if (endX === undefined || endY === undefined || isNaN(endX) || isNaN(endY)) {
          return;
        }
      }

      // 计算移动距离
      const moveDistance = Math.sqrt(
        Math.pow(endX - this.touchStartX, 2) +
        Math.pow(endY - this.touchStartY, 2)
      );

      // 如果是短时间轻击且移动距离小，认为是点击
      if (touchDuration <= this.maxTapDuration && moveDistance <= this.maxTapDistance && !this.isScrolling) {
        // 调整点击坐标，考虑滚动偏移
        const adjustedY = this.touchStartY + this.scrollOffset;
        this.handleLevelScreenTouch(this.touchStartX, adjustedY);

        // 点击后停止滚动
        this.scrollVelocity = 0;
      }

      // 🎯 修复：手指离开屏幕，允许惯性滚动
      this.isFingerOnScreen = false;

      // 如果有滚动速度，不要立即重置isScrolling，让惯性滚动继续
      if (Math.abs(this.scrollVelocity) <= this.minScrollVelocity) {
        this.isScrolling = false;
      }

      return;
    }

    // 重置所有道具按钮的激活状态
    Object.values(this.itemBtnAreas).forEach(area => {
      area.isActive = false;
    });

    // 重置活动方向状态
    if (this.activeDirection) {
      this.activeDirection = null;
    }
    
    // 处理游戏界面的触摸结束事件
    if (this.currentScreen === 'game') {
      // console.log(`🖐️ [${now}] 手指离开屏幕，停止所有手势操作`);

      // 处理按钮释放
      if (this.isButtonPressed && this.pressedButton) {
        // 发送按钮释放事件
        this.emit('move', { direction: this.pressedButton, pressed: false });

        // 如果是快速下落按钮，停止快速下落
        if (this.pressedButton === 'down') {
          this.stopFastDrop();
        }

        // 重置按钮状态
        this.isButtonPressed = false;
        this.pressedButton = null;
      }

      // 立即重置所有状态
      this.isFingerOnScreen = false;
      this.isGestureTracking = false;

      // 停止跟手拖拽
      this.stopDragging();

      // 停止快速下落（防止手势触发的快速下落）
      this.stopFastDrop();

      console.log(`✅ [${now}] 所有手势状态已重置`);

      // 🔥 修复：暂停状态下的特殊处理
      if (isPaused) {
        // console.log(`⏸️ [${now}] 暂停状态下的触摸处理`);
        
        const touchDuration = now - this.touchStartTime;
        
        // 获取触摸结束位置
        let endX = this.touchStartX;
        let endY = this.touchStartY;
        
        if (event.changedTouches && event.changedTouches.length > 0) {
          const touch = event.changedTouches[0];
          endX = touch.pageX !== undefined ? touch.pageX : touch.clientX;
          endY = touch.pageY !== undefined ? touch.pageY : touch.clientY;
        }
        
        // 计算触摸移动距离
        const moveDistance = Math.sqrt(
          Math.pow(endX - this.touchStartX, 2) +
          Math.pow(endY - this.touchStartY, 2)
        );
        
        // 只有短时间轻击且移动距离小才处理
        if (touchDuration <= this.maxTapDuration && moveDistance <= this.maxTapDistance) {
          // 🔥 修复：检查是否在touchstart时已经处理过暂停按钮
          if (this.lastProcessedButtonType === 'pause') {
            console.log(`⏸️ [${now}] 暂停按钮已在touchstart中处理，跳过touchend处理`);
            return; // 避免重复处理
          }
          
          // 检查是否点击了暂停按钮区域
          if (this.isPointInArea(this.touchStartX, this.touchStartY, this.pauseArea)) {
            console.log(`⏸️ [${now}] 暂停状态下点击暂停按钮，恢复游戏`);
            this.emit('pause'); // 触发暂停/恢复切换
          } else {
            // 暂停状态下点击其他区域，也恢复游戏（保持原有行为）
            console.log(`⏸️ [${now}] 暂停状态下点击屏幕，恢复游戏`);
            this.emit('pause'); // 触发暂停/恢复切换
          }
        }
        return; // 暂停状态下不执行后续逻辑
      }

      const touchDuration = now - this.touchStartTime;

      // 获取触摸结束位置
      let endX = 0;
      let endY = 0;

      // 检查是否有触摸点，有的话获取位置
      if (event.changedTouches && event.changedTouches.length > 0) {
        const touch = event.changedTouches[0];
        endX = touch.pageX !== undefined ? touch.pageX : touch.clientX;
        endY = touch.pageY !== undefined ? touch.pageY : touch.clientY;
      }

      // 计算触摸移动距离
      const moveDistance = Math.sqrt(
        Math.pow(endX - this.touchStartX, 2) +
        Math.pow(endY - this.touchStartY, 2)
      );

      // 🔥 修复：只有在非暂停状态下才处理旋转逻辑
      // 如果是短时间轻击且移动距离小，触发旋转
      if (touchDuration < this.maxTapDuration && moveDistance < this.maxTapDistance && !buttonPressed && !this.isDragging && !this.isGestureFastDrop) {
        this.emit('rotate');
      }

      // 清理所有手势状态
      this.cleanupGestureStates();
    }
  }

  /**
   * 处理关卡选择界面的触摸事件
   * @param {number} x - 触摸点X坐标
   * @param {number} y - 触摸点Y坐标（已调整滚动偏移）
   */
  handleLevelScreenTouch(x, y) {
    const now = Date.now();

    // 检查坐标有效性
    if (x === undefined || y === undefined || isNaN(x) || isNaN(y)) {
      console.log(`❌ [${now}] 关卡选择界面收到无效坐标: (${x}, ${y})`);
      return;
    }

    console.log(`🎮 [${now}] 关卡选择界面处理触摸: (${x}, ${y})`);

    // 检查是否点击了关卡按钮
    for (const area of this.btnAreas.levels) {
      if (x >= area.startX && x <= area.endX && y >= area.startY && y <= area.endY) {
        // 检查关卡是否解锁
        if (area.level && area.level.unlocked) {
          console.log(`🎯 [${now}] 选择关卡: ${area.levelId} 区域:(${area.startX},${area.startY})->(${area.endX},${area.endY})`);
          // 触发关卡选择事件
          this.emit('level:select', { levelId: area.levelId });
        } else {
          console.log(`🔒 [${now}] 关卡未解锁: ${area.levelId}`);
          // 播放提示音效
          if (GameGlobal.musicManager && typeof GameGlobal.musicManager.playBuzz === 'function') {
            GameGlobal.musicManager.playBuzz();
          } else {
            console.warn('音效管理器或playBuzz方法不可用');
          }
        }
        return;
      }
    }

    console.log(`🎮 [${now}] 触摸未命中任何关卡按钮`);
  }

  /**
   * 处理关卡开始界面的触摸事件
   * @param {number} x - 触摸点X坐标
   * @param {number} y - 触摸点Y坐标
   */
  handleLevelStartTouch(x, y) {
    console.log(`🎯 处理关卡开始界面触摸: (${x}, ${y})`);
    
    // 检查是否点击了开始游戏按钮
    const startArea = this.btnAreas.levelStartGame;
    console.log(`🔍 开始游戏按钮区域: x[${startArea.startX}-${startArea.endX}], y[${startArea.startY}-${startArea.endY}]`);
    
    if (x >= startArea.startX && x <= startArea.endX && y >= startArea.startY && y <= startArea.endY) {
      console.log('✅ 点击开始游戏按钮 - 发送start:game事件');
      this.emit('start:game');
      return;
    }

    // 检查是否点击了返回按钮
    const backArea = this.btnAreas.levelStartBack;
    console.log(`🔍 返回按钮区域: x[${backArea.startX}-${backArea.endX}], y[${backArea.startY}-${backArea.endY}]`);
    
    if (x >= backArea.startX && x <= backArea.endX && y >= backArea.startY && y <= backArea.endY) {
      console.log('✅ 点击返回按钮 - 发送back事件');
      this.emit('back');
      return;
    }
    
    console.log('❌ 触摸未命中任何按钮');
  }
  
  /**
   * 处理游戏界面的触摸事件
   * @param {number} x - 触摸点的X坐标
   * @param {number} y - 触摸点的Y坐标
   */
  handleGameScreenTouch(x, y) {
    const now = Date.now();

    // 🛡️ 防重复调用机制
    if (this.lastHandleGameScreenTouchTime && (now - this.lastHandleGameScreenTouchTime) < 50) {
      return;
    }
    this.lastHandleGameScreenTouchTime = now;

    // 验证坐标有效性
    if (x === undefined || y === undefined || isNaN(x) || isNaN(y)) {
      return;
    }

    // 🔥 标记处理过的按钮类型，避免在touchEnd中重复处理
    this.lastProcessedButtonType = null;

    // 首先检查是否点击了暂停按钮
    if (this.isPointInArea(x, y, this.pauseArea)) {
      console.log('点击暂停按钮');
      this.lastProcessedButtonType = 'pause'; // 🔥 标记已处理暂停按钮
      // 暂停按钮的去抖动将在main.js中的pauseGame方法中处理
      this.emit('pause');
      return;
    }
    
    // 检查是否点击了方向按钮
    for (const [direction, area] of Object.entries(this.directionBtnAreas)) {
      if (this.isPointInArea(x, y, area)) {
        this.lastProcessedButtonType = 'direction'; // 🔥 标记已处理方向按钮

        if (direction === 'rotate') {
          // 旋转是瞬时动作，直接发送信号
          this.emit('rotate');
        } else {
          // 记录按钮按下状态
          this.isButtonPressed = true;
          this.pressedButton = direction;
          this.activeDirection = direction;

          // 发送移动信号，并标记为按下状态
          this.emit('move', { direction, pressed: true });

          // 特殊处理快速下落按钮
          if (direction === 'down') {
            this.isFastDropping = true;
          }
        }
        return;
      }
    }
    
    // 检查是否点击了道具按钮
    for (const [itemType, area] of Object.entries(this.itemBtnAreas)) {
      if (this.isPointInArea(x, y, area)) {
        console.log('点击道具按钮:', itemType);
        this.lastProcessedButtonType = 'item'; // 🔥 标记已处理道具按钮
        // 标记按钮为激活状态（视觉效果）
        area.isActive = true;
        
        // 检查道具是否可以使用
        const itemManager = GameGlobal.main && GameGlobal.main.itemManager;
        if (itemManager) {
          const itemInfo = itemManager.getItemInfo(itemType);
          console.log('道具信息:', itemInfo);
          
          if (itemInfo && itemInfo.hasUses && itemInfo.isReady) {
            console.log('触发useItem事件:', itemType);
            // 发送使用道具信号
            this.emit('useItem', { type: itemType });
          } else {
            console.log('道具不可用');
            // 道具不可用，播放提示音效
            if (GameGlobal.musicManager && typeof GameGlobal.musicManager.playBuzz === 'function') {
              GameGlobal.musicManager.playBuzz();
            } else {
              console.warn('音效管理器或playBuzz方法不可用');
            }
          }
        } else {
          console.log('未找到道具管理器');
        }
        return;
      }
    }
  }
  
  /**
   * 判断点是否在游戏中区域内（方块下落区域）
   * @param {number} x - 点的X坐标
   * @param {number} y - 点的Y坐标
   * @returns {boolean} - 点是否在游戏中区域内
   */
  isPointInGameArea(x, y) {
    // 游戏区域为网格区域，不包括顶部状态栏（60px高）
    return x >= 0 && x < (SCREEN_WIDTH - 90) && y >= 60 && y <= (SCREEN_HEIGHT - 100);
  }
  
  /**
   * 判断点是否在区域内
   * @param {number} x - 点的X坐标
   * @param {number} y - 点的Y坐标
   * @param {Object} area - 区域对象
   * @returns {boolean} - 点是否在区域内
   */
  isPointInArea(x, y, area) {
    return x >= area.startX && x <= area.endX && y >= area.startY && y <= area.endY;
  }
  
  /**
   * 处理结果界面的触摸事件
   * @param {number} x - 触摸X坐标
   * @param {number} y - 触摸Y坐标
   */
  handleResultScreenTouch(x, y) {
    // 检查是否点击了重新开始按钮
    if (
      x >= this.btnAreas.restart.startX && 
      x <= this.btnAreas.restart.endX && 
      y >= this.btnAreas.restart.startY && 
      y <= this.btnAreas.restart.endY
    ) {
      if (this.currentScreen === 'levelcomplete') {
        // 关卡完成，进入下一关
        this.emit('next:level');
      } else {
        // 游戏结束或关卡失败，重新开始
        this.emit('restart');
      }
      return;
    }
  }

  /**
   * 渲染调试信息
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  renderDebugInfo(ctx) {
    // 只在调试模式开启时显示
    if (!isDebugMode()) {
      return;
    }

    // 绘制调试信息背景
    ctx.fillStyle = 'rgba(255, 0, 0, 0.8)';
    ctx.fillRect(10, 10, 200, 60);

    // 绘制调试模式标识
    this.setFont(ctx, 16, '#ffffff');
    ctx.textAlign = 'left';
    ctx.fillText('🐛 调试模式', 20, 30);

    // 绘制分数调整信息
    this.setFont(ctx, 12, '#ffff00');
    ctx.fillText('分数要求: 原来的1/10', 20, 50);

    // 绘制提示信息
    this.setFont(ctx, 10, '#cccccc');
    ctx.fillText('控制台输入 toggleDebugMode() 切换', 20, 65);

    // 重置文本对齐
    ctx.textAlign = 'left';
  }
}
