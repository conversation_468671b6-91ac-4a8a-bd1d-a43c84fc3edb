/**
 * 匹配检测器
 * 负责检测网格中满足消除条件的方块
 */
import { BLOCK_EFFECTS } from './block.js';

export default class MatchChecker {
  /**
   * 创建一个匹配检测器
   * @param {Grid} grid - 游戏网格
   */
  constructor(grid) {
    this.grid = grid;
    this.matchedBlocks = new Set(); // 使用Set避免重复
    this.effectBlocks = []; // 存储触发特殊效果的方块
  }
  
  /**
   * 检查两个方块是否可以匹配
   * @param {Block} block1 - 第一个方块
   * @param {Block} block2 - 第二个方块
   * @returns {boolean} 如果可以匹配返回true
   * @private
   */
  _canMatch(block1, block2) {
    if (!block1 || !block2) return false;
    if (block1.isDestroying || block2.isDestroying) return false;

    // 彩虹方块可以与任意颜色匹配
    if (block1.effect === BLOCK_EFFECTS.RAINBOW || block2.effect === BLOCK_EFFECTS.RAINBOW) {
      return true;
    }

    // 颜色相同即可匹配，不受冰冻状态影响
    return block1.color === block2.color;
  }
  
  /**
   * 检查是否有匹配
   * @returns {boolean} 如果有匹配返回true
   */
  checkMatches() {
    this.matchedBlocks.clear();
    let hasMatches = false;

    // 🔧 修复：使用贪婪匹配算法，找到最长的连续同色方块序列

    // 检查水平匹配（贪婪算法）
    for (let row = 0; row < this.grid.rows; row++) {
      const horizontalMatches = this._findHorizontalMatches(row);
      if (horizontalMatches.length > 0) {
        horizontalMatches.forEach(match => {
          match.blocks.forEach(block => this.matchedBlocks.add(block));
        });
        hasMatches = true;
      }
    }

    // 检查垂直匹配（贪婪算法）
    for (let col = 0; col < this.grid.cols; col++) {
      const verticalMatches = this._findVerticalMatches(col);
      if (verticalMatches.length > 0) {
        verticalMatches.forEach(match => {
          match.blocks.forEach(block => this.matchedBlocks.add(block));
        });
        hasMatches = true;
      }
    }

    if (hasMatches) {
      console.log(`🔍 贪婪匹配找到 ${this.matchedBlocks.size} 个匹配方块`);
    }

    return hasMatches;
  }

  /**
   * 查找水平方向的贪婪匹配
   * @param {number} row - 行索引
   * @returns {Array} 匹配序列数组
   * @private
   */
  _findHorizontalMatches(row) {
    const matches = [];
    let currentMatch = [];
    let currentColor = null;

    for (let col = 0; col < this.grid.cols; col++) {
      const block = this.grid.getBlock(row, col);
      const blockColor = this._getBlockMatchColor(block);

      // 🔧 修复：改进冰冻方块处理逻辑
      if (block && blockColor === currentColor && currentColor !== null) {
        // 继续当前匹配序列
        currentMatch.push(block);
      } else {
        // 检查当前序列是否满足匹配条件
        if (currentMatch.length >= 3) {
          // 🔧 修复：冰冻方块可以参与匹配，只要总数≥3即可
          const nonFrozenBlocks = currentMatch.filter(b => !b.isFrozen);
          matches.push({
            type: 'horizontal',
            row: row,
            startCol: col - currentMatch.length,
            endCol: col - 1,
            blocks: [...currentMatch],
            length: currentMatch.length,
            nonFrozenCount: nonFrozenBlocks.length
          });
          console.log(`🔍 水平贪婪匹配: 第${row}行, 列${col - currentMatch.length}-${col - 1}, 长度${currentMatch.length}, 非冰冻${nonFrozenBlocks.length}`);
        }

        // 开始新的匹配序列
        if (block && blockColor !== null) {
          currentMatch = [block];
          currentColor = blockColor;
        } else {
          currentMatch = [];
          currentColor = null;
        }
      }
    }

    // 检查行末的匹配序列
    if (currentMatch.length >= 3) {
      // 🔧 修复：冰冻方块可以参与匹配，只要总数≥3即可
      const nonFrozenBlocks = currentMatch.filter(b => !b.isFrozen);
      matches.push({
        type: 'horizontal',
        row: row,
        startCol: this.grid.cols - currentMatch.length,
        endCol: this.grid.cols - 1,
        blocks: [...currentMatch],
        length: currentMatch.length,
        nonFrozenCount: nonFrozenBlocks.length
      });
      console.log(`🔍 水平贪婪匹配(行末): 第${row}行, 列${this.grid.cols - currentMatch.length}-${this.grid.cols - 1}, 长度${currentMatch.length}, 非冰冻${nonFrozenBlocks.length}`);
    }

    return matches;
  }

  /**
   * 查找垂直方向的贪婪匹配
   * @param {number} col - 列索引
   * @returns {Array} 匹配序列数组
   * @private
   */
  _findVerticalMatches(col) {
    const matches = [];
    let currentMatch = [];
    let currentColor = null;

    for (let row = 0; row < this.grid.rows; row++) {
      const block = this.grid.getBlock(row, col);
      const blockColor = this._getBlockMatchColor(block);

      // 🔧 修复：改进冰冻方块处理逻辑
      if (block && blockColor === currentColor && currentColor !== null) {
        // 继续当前匹配序列
        currentMatch.push(block);
      } else {
        // 检查当前序列是否满足匹配条件
        if (currentMatch.length >= 3) {
          // 🔧 修复：冰冻方块可以参与匹配，只要总数≥3即可
          const nonFrozenBlocks = currentMatch.filter(b => !b.isFrozen);
          matches.push({
            type: 'vertical',
            col: col,
            startRow: row - currentMatch.length,
            endRow: row - 1,
            blocks: [...currentMatch],
            length: currentMatch.length,
            nonFrozenCount: nonFrozenBlocks.length
          });
          console.log(`🔍 垂直贪婪匹配: 第${col}列, 行${row - currentMatch.length}-${row - 1}, 长度${currentMatch.length}, 非冰冻${nonFrozenBlocks.length}`);
        }

        // 开始新的匹配序列
        if (block && blockColor !== null) {
          currentMatch = [block];
          currentColor = blockColor;
        } else {
          currentMatch = [];
          currentColor = null;
        }
      }
    }

    // 检查列末的匹配序列
    if (currentMatch.length >= 3) {
      // 🔧 修复：冰冻方块可以参与匹配，只要总数≥3即可
      const nonFrozenBlocks = currentMatch.filter(b => !b.isFrozen);
      matches.push({
        type: 'vertical',
        col: col,
        startRow: this.grid.rows - currentMatch.length,
        endRow: this.grid.rows - 1,
        blocks: [...currentMatch],
        length: currentMatch.length,
        nonFrozenCount: nonFrozenBlocks.length
      });
      console.log(`🔍 垂直贪婪匹配(列末): 第${col}列, 行${this.grid.rows - currentMatch.length}-${this.grid.rows - 1}, 长度${currentMatch.length}, 非冰冻${nonFrozenBlocks.length}`);
    }

    return matches;
  }

  /**
   * 获取方块的匹配颜色
   * @param {Block} block - 方块对象
   * @returns {string|null} 匹配颜色，如果不能匹配则返回null
   * @private
   */
  _getBlockMatchColor(block) {
    if (!block) return null;

    // 🔧 修复：冰冻方块可以参与匹配检测，但需要特殊处理
    // 冰冻方块仍然有颜色，可以参与连续性检测
    return block.color;
  }
  
  /**
   * 移除匹配的方块并返回特殊效果
   * @returns {Array} 需要应用的特殊效果列表
   */
  removeMatches() {
    const effects = [];
    const blocksToRemove = new Set();
    
    // 添加调试日志
    console.log(`开始处理 ${this.matchedBlocks.size} 个匹配的方块`);
    
    // 先处理所有匹配的方块
    for (const block of this.matchedBlocks) {
      if (block.effect === BLOCK_EFFECTS.FROZEN) {
        // 冰冻方块需要两次匹配才能消除
        if (block.isFrozen) {
          // 第一次匹配，解除冰冻状态
          // 🔥 重要：保持位置信息不变，只改变效果
          const originalRow = block.row;
          const originalCol = block.col;
          block.setEffect(BLOCK_EFFECTS.NONE);
          // 🔥 修复：确保位置信息不被重置
          block.row = originalRow;
          block.col = originalCol;
          // 不要将这个方块加入到要移除的列表中
          console.log(`冰冻方块解冻: (${originalRow}, ${originalCol})`);
        } else {
          // 第二次匹配，可以消除
          blocksToRemove.add(block);
          console.log(`解冻后的方块消除: (${block.row}, ${block.col})`);
        }
      } else {
        // 非冰冻方块直接加入移除列表
        blocksToRemove.add(block);
        
        // 检查是否有特殊效果需要触发
        if (block.effect !== BLOCK_EFFECTS.NONE) {
          console.log(`发现特效方块: ${block.effect}, 位置: (${block.row}, ${block.col})`);
          effects.push({
            type: block.effect,
            row: block.row,
            col: block.col
          });
        }
      }
    }
    
    // 更新匹配列表，只保留真正要消除的方块
    this.matchedBlocks = blocksToRemove;
    
    console.log(`收集到 ${effects.length} 个特效需要触发:`, effects);
    
    return effects;
  }
  
  /**
   * 从网格中移除已完成消除动画的方块
   * @returns {number} 被移除的方块数量
   */
  finalizeRemoval() {
    let removedCount = 0;
    
    // 创建一个要移除方块的临时数组
    const blocksToRemove = [];
    
    // 首先收集所有要移除的方块及其位置
    for (let row = 0; row < this.grid.rows; row++) {
      for (let col = 0; col < this.grid.cols; col++) {
        const block = this.grid.getBlock(row, col);
        
        // 如果方块存在且在匹配列表中
        if (block && this.matchedBlocks.has(block)) {
          console.log(`检查方块[${row},${col}]动画状态: isDestroying=${block.isDestroying}, progress=${block.destroyProgress}/${block.destroyDuration}`);
          
          // 强制更新动画状态，如果动画已进行足够长时间，直接完成它
          if (block.isDestroying && block.destroyProgress >= block.destroyDuration) {
            block.isDestroying = false;
            block.destroyProgress = 0;
            blocksToRemove.push({ row, col, block });
            console.log(`方块[${row},${col}]动画已完成，将被移除`);
            continue;
          }
          
          // 正常检查动画是否完成
          if (block.updateDestroyAnimation()) {
            blocksToRemove.push({ row, col, block });
            console.log(`方块[${row},${col}]被标记为移除`);
          }
        }
      }
    }
    
    console.log(`收集到 ${blocksToRemove.length} 个方块需要移除`);
    
    // 然后移除这些方块
    for (const { row, col, block } of blocksToRemove) {
      this.grid.removeBlock(row, col);
      this.matchedBlocks.delete(block); // 从匹配列表中移除
      removedCount++;
      console.log(`已移除方块[${row},${col}]`);
    }
    
    // 打印调试信息
    console.log(`匹配列表中还有 ${this.matchedBlocks.size} 个方块未完成消除动画，本次移除了 ${removedCount} 个`);
    
    return removedCount;
  }
  
  /**
   * 应用特殊效果
   * @param {Array} effects - 特殊效果数组
   * @returns {Set} 受影响的方块集合
   */
  applyEffects(effects) {
    const affectedBlocks = new Set();
    
    // 记录已处理过的方块位置，避免重复
    const processedPositions = new Set();
    
    for (const effect of effects) {
      console.log(`应用特效: ${effect.type}, 位置: (${effect.row}, ${effect.col})`);
      
      if (effect.type === BLOCK_EFFECTS.MINE) {
        // 处理地雷效果，影响3x3区域
        const { row, col, range } = effect;
        
        for (let r = row - range; r <= row + range; r++) {
          for (let c = col - range; c <= col + range; c++) {
            // 跳过中心点（已经作为匹配的一部分被处理）
            if (r === row && c === col) continue;
            
            // 检查是否在网格范围内
            if (r >= 0 && r < this.grid.rows && c >= 0 && c < this.grid.cols) {
              // 避免重复处理同一位置
              const posKey = `${r},${c}`;
              if (processedPositions.has(posKey)) continue;
              processedPositions.add(posKey);
              
              const block = this.grid.getBlock(r, c);
              
              if (block) {
                console.log(`特效影响方块: (${r}, ${c})`);
                
                // 如果是冰冻方块，先消耗冰冻效果
                if (block.effect === BLOCK_EFFECTS.FROZEN) {
                  block.applyEffect();
                  console.log(`冰冻方块效果已消耗: (${r}, ${c})`);
                } else {
                  // 标记为受影响，稍后会被消除
                  // 保存方块位置信息，便于后续找到并移除
                  block.row = r;
                  block.col = c;
                  
                  affectedBlocks.add(block);
                  block.startDestroyAnimation();
                }
              }
            }
          }
        }
      }
      // 这里可以添加其他特殊效果的处理逻辑
    }
    
    console.log(`特效总共影响了 ${affectedBlocks.size} 个方块`);
    return affectedBlocks;
  }
  
  /**
   * 获取匹配的数量
   * @returns {number} 匹配的方块数量
   */
  getMatchCount() {
    return this.matchedBlocks.size;
  }
} 