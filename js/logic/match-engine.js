/**
 * 匹配引擎
 * 负责处理方块匹配检测、消除和特效应用
 */
import Emitter from '../libs/tinyemitter.js';
import { BLOCK_EFFECTS } from '../game/block.js';

export default class MatchEngine extends Emitter {
  constructor(grid, comboSystem, matchChecker) {
    super();
    
    this.grid = grid;
    this.comboSystem = comboSystem;
    this.matchChecker = matchChecker;
    
    // 匹配状态
    this.hasActiveMatches = false;
    this.matchedBlocks = new Set();
    
    console.log('✅ MatchEngine初始化完成');
  }
  
  /**
   * 检查匹配
   * @param {Array} blocksToCheck - 需要检查的方块
   * @returns {boolean} 是否有匹配
   */
  checkMatches(blocksToCheck = []) {
    console.log('🔍 MatchEngine检查匹配', { blocksToCheck: blocksToCheck.length });
    
    // 委托给匹配检测器
    const hasMatches = this.matchChecker.checkMatches();
    
    if (hasMatches) {
      this.hasActiveMatches = true;
      this.matchedBlocks = new Set(this.matchChecker.matchedBlocks);
      
      console.log(`✅ 发现匹配: ${this.matchedBlocks.size} 个方块`);
      
      // 发出匹配事件
      this.emit('matchfound', {
        matchedBlocks: Array.from(this.matchedBlocks),
        matchCount: this.matchedBlocks.size
      });
    }
    
    return hasMatches;
  }
  
  /**
   * 检查是否有新的匹配
   * @returns {boolean} 是否有新匹配
   */
  checkForNewMatches() {
    return this.checkMatches();
  }
  
  /**
   * 应用特效
   * @returns {Promise} 特效应用完成的Promise
   */
  async applyEffects() {
    if (!this.hasActiveMatches) return;
    
    console.log('🎭 开始应用特效');
    
    // 获取特效列表
    const effects = this.matchChecker.removeMatches();
    
    if (effects.length > 0) {
      console.log(`应用 ${effects.length} 个特效`);
      
      // 应用所有特效
      const affectedBlocks = this.matchChecker.applyEffects(effects);
      
      // 发出特效应用事件
      this.emit('effectsapplied', {
        effectsCount: effects.length,
        affectedBlocks: affectedBlocks.size
      });
    }
    
    console.log('✅ 特效应用完成');
  }
  
  /**
   * 移除匹配的方块
   * @returns {Array} 被移除的方块位置
   */
  removeMatchedBlocks() {
    if (!this.hasActiveMatches) return [];
    
    console.log('🗑️ 开始移除匹配的方块');
    
    const removedPositions = [];
    
    // 强制完成所有方块的消除动画
    for (const block of this.matchedBlocks) {
      if (block && typeof block.row === 'number' && typeof block.col === 'number') {
        // 开始或强制完成消除动画
        if (!block.isDestroying) {
          block.startDestroyAnimation();
        }
        
        // 强制完成动画
        block.isDestroying = false;
        block.destroyProgress = 0;
        
        // 从网格中移除
        this.grid.removeBlock(block.row, block.col);
        
        // 记录被移除的位置
        removedPositions.push({
          row: block.row,
          col: block.col
        });
        
        console.log(`  移除方块 [${block.row}, ${block.col}]`);
      }
    }
    
    // 清理状态
    this.matchedBlocks.clear();
    this.matchChecker.matchedBlocks.clear();
    this.hasActiveMatches = false;
    
    console.log(`✅ 移除完成: ${removedPositions.length} 个方块`);
    
    // 发出方块移除事件
    this.emit('blocksremoved', {
      count: removedPositions.length,
      positions: removedPositions
    });
    
    return removedPositions;
  }
  
  /**
   * 获取匹配状态
   * @returns {Object} 匹配状态信息
   */
  getMatchStatus() {
    return {
      hasActiveMatches: this.hasActiveMatches,
      matchedBlocksCount: this.matchedBlocks.size
    };
  }
  
  /**
   * 重置匹配引擎
   */
  reset() {
    this.hasActiveMatches = false;
    this.matchedBlocks.clear();
    this.matchChecker.matchedBlocks.clear();
    console.log('🔄 MatchEngine 重置完成');
  }
  
  /**
   * 销毁匹配引擎
   */
  destroy() {
    this.removeAllListeners();
    this.reset();
    console.log('🗑️ MatchEngine 销毁完成');
  }
} 